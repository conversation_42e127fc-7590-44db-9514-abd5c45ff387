"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9783],{

/***/ 9783:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(677);
/* harmony import */ var antd_es_typography__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5475);
/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1850);
/* harmony import */ var antd_es_space__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8392);
/* harmony import */ var antd_es_row__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7152);
/* harmony import */ var antd_es_col__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6370);
/* harmony import */ var antd_es_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(2395);
/* harmony import */ var antd_es_input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(7355);
/* harmony import */ var antd_es_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(4358);
/* harmony import */ var _ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(8244);
/* harmony import */ var _ant_design_icons_es_icons_LayoutOutlined__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(7290);
/* harmony import */ var _ant_design_icons_es_icons_BgColorsOutlined__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(271);
/* harmony import */ var _ant_design_icons_es_icons_CodeOutlined__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(5937);
/* harmony import */ var _ant_design_icons_es_icons_PlusOutlined__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(8940);
/* harmony import */ var _ant_design_icons_es_icons_DeleteOutlined__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(9499);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
















var Title = antd_es_typography__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.Title,
  Text = antd_es_typography__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.Text;
var TextArea = antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A.TextArea;

/**
 * Lightweight App Builder - Optimized for Bundle Size
 * Core app building functionality without heavy dependencies
 */
var LightweightAppBuilder = function LightweightAppBuilder() {
  // Component Builder State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    components = _useState2[0],
    setComponents = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    componentName = _useState4[0],
    setComponentName = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('button'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    componentType = _useState6[0],
    setComponentType = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('{}'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    componentProps = _useState8[0],
    setComponentProps = _useState8[1];

  // Layout Designer State
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    layouts = _useState0[0],
    setLayouts = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    layoutName = _useState10[0],
    setLayoutName = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    selectedLayout = _useState12[0],
    setSelectedLayout = _useState12[1];

  // Theme Manager State
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState13, 2),
    themes = _useState14[0],
    setThemes = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
      name: 'Default',
      primaryColor: '#1890ff',
      backgroundColor: '#ffffff',
      textColor: '#000000'
    }),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState15, 2),
    currentTheme = _useState16[0],
    setCurrentTheme = _useState16[1];

  // Code Export State
  var _useState17 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState17, 2),
    generatedCode = _useState18[0],
    setGeneratedCode = _useState18[1];

  // Component Types
  var componentTypes = [{
    value: 'button',
    label: 'Button'
  }, {
    value: 'input',
    label: 'Input'
  }, {
    value: 'text',
    label: 'Text'
  }, {
    value: 'card',
    label: 'Card'
  }, {
    value: 'container',
    label: 'Container'
  }];

  // Component Builder Functions
  var addComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (!componentName.trim()) return;
    try {
      var props = JSON.parse(componentProps);
      var newComponent = {
        id: Date.now().toString(),
        name: componentName.trim(),
        type: componentType,
        props: props,
        createdAt: new Date().toISOString()
      };
      setComponents(function (prev) {
        return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newComponent]);
      });
      setComponentName('');
      setComponentProps('{}');
    } catch (error) {
      alert('Invalid JSON in props: ' + error.message);
    }
  }, [componentName, componentType, componentProps]);
  var removeComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (id) {
    setComponents(function (prev) {
      return prev.filter(function (comp) {
        return comp.id !== id;
      });
    });
  }, []);

  // Layout Designer Functions
  var addLayout = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (!layoutName.trim()) return;
    var newLayout = {
      id: Date.now().toString(),
      name: layoutName.trim(),
      components: (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(components),
      createdAt: new Date().toISOString()
    };
    setLayouts(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newLayout]);
    });
    setLayoutName('');
  }, [layoutName, components]);

  // Theme Manager Functions
  var saveTheme = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    var newTheme = _objectSpread(_objectSpread({
      id: Date.now().toString()
    }, currentTheme), {}, {
      createdAt: new Date().toISOString()
    });
    setThemes(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newTheme]);
    });
  }, [currentTheme]);

  // Code Export Functions
  var generateCode = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    var code = "\n// Generated App Builder Code\nimport React from 'react';\n\nconst GeneratedApp = () => {\n  return (\n    <div style={{\n      backgroundColor: '".concat(currentTheme.backgroundColor, "',\n      color: '").concat(currentTheme.textColor, "',\n      minHeight: '100vh',\n      padding: '20px'\n    }}>\n      <h1 style={{ color: '").concat(currentTheme.primaryColor, "' }}>\n        Generated Application\n      </h1>\n      \n      {/* Components */}\n      ").concat(components.map(function (comp) {
      return "\n      <div key=\"".concat(comp.id, "\" style={{ margin: '10px 0' }}>\n        {/* ").concat(comp.name, " (").concat(comp.type, ") */}\n        ").concat(generateComponentCode(comp), "\n      </div>");
    }).join(''), "\n    </div>\n  );\n};\n\nexport default GeneratedApp;\n");
    setGeneratedCode(code);
  }, [components, currentTheme]);
  var generateComponentCode = function generateComponentCode(component) {
    switch (component.type) {
      case 'button':
        return "<button style={{ backgroundColor: '".concat(currentTheme.primaryColor, "', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px' }}>").concat(component.name, "</button>");
      case 'input':
        return "<input placeholder=\"".concat(component.name, "\" style={{ padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }} />");
      case 'text':
        return "<p style={{ color: '".concat(currentTheme.textColor, "' }}>").concat(component.name, "</p>");
      case 'card':
        return "<div style={{ border: '1px solid #ccc', borderRadius: '8px', padding: '16px', backgroundColor: 'white' }}>".concat(component.name, "</div>");
      case 'container':
        return "<div style={{ padding: '16px', border: '1px dashed #ccc' }}>".concat(component.name, "</div>");
      default:
        return "<div>".concat(component.name, "</div>");
    }
  };

  // Tab Items
  var tabItems = [{
    key: 'components',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A, null), "Components"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Component Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      placeholder: "Component Name",
      value: componentName,
      onChange: function onChange(e) {
        return setComponentName(e.target.value);
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_select__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .A, {
      value: componentType,
      onChange: setComponentType,
      style: {
        width: '100%'
      },
      options: componentTypes
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, {
      placeholder: "Component Props (JSON)",
      value: componentProps,
      onChange: function onChange(e) {
        return setComponentProps(e.target.value);
      },
      rows: 3
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_PlusOutlined__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .A, null),
      onClick: addComponent
    }, "Add Component")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4,
      style: {
        marginTop: '24px'
      }
    }, "Components (", components.length, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        maxHeight: '300px',
        overflowY: 'auto'
      }
    }, components.map(function (comp) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: comp.id,
        size: "small",
        style: {
          marginBottom: '8px'
        },
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
          type: "text",
          danger: true,
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_DeleteOutlined__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .A, null),
          onClick: function onClick() {
            return removeComponent(comp.id);
          }
        })
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, comp.name), " (", comp.type, ")");
    })))
  }, {
    key: 'layout',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_LayoutOutlined__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .A, null), "Layout"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Layout Designer"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      placeholder: "Layout Name",
      value: layoutName,
      onChange: function onChange(e) {
        return setLayoutName(e.target.value);
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      onClick: addLayout
    }, "Save Layout")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4,
      style: {
        marginTop: '24px'
      }
    }, "Saved Layouts (", layouts.length, ")"), layouts.map(function (layout) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: layout.id,
        size: "small",
        style: {
          marginBottom: '8px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, layout.name), " - ", layout.components.length, " components");
    }))
  }, {
    key: 'theme',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_BgColorsOutlined__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .A, null), "Theme"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Theme Manager"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Theme Name:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      value: currentTheme.name,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            name: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Primary Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      type: "color",
      value: currentTheme.primaryColor,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            primaryColor: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Background Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      type: "color",
      value: currentTheme.backgroundColor,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            backgroundColor: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Text Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      type: "color",
      value: currentTheme.textColor,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            textColor: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      onClick: saveTheme
    }, "Save Theme")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4,
      style: {
        marginTop: '24px'
      }
    }, "Saved Themes (", themes.length, ")"), themes.map(function (theme) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: theme.id,
        size: "small",
        style: {
          marginBottom: '8px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, theme.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px',
          marginTop: '4px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '20px',
          height: '20px',
          backgroundColor: theme.primaryColor,
          borderRadius: '2px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '20px',
          height: '20px',
          backgroundColor: theme.backgroundColor,
          border: '1px solid #ccc',
          borderRadius: '2px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '20px',
          height: '20px',
          backgroundColor: theme.textColor,
          borderRadius: '2px'
        }
      })));
    }))
  }, {
    key: 'export',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_CodeOutlined__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .A, null), "Export"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Code Export"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      onClick: generateCode
    }, "Generate Code"), generatedCode && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, {
      value: generatedCode,
      rows: 20,
      readOnly: true,
      style: {
        fontFamily: 'monospace',
        fontSize: '12px'
      }
    })))
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 1
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A, {
    style: {
      marginRight: '16px',
      color: '#1890ff'
    }
  }), "Lightweight App Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '16px',
      display: 'block',
      marginBottom: '24px'
    }
  }, "Build applications visually with our optimized, lightweight interface"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_tabs__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, {
    items: tabItems,
    size: "large"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LightweightAppBuilder);

/***/ })

}]);