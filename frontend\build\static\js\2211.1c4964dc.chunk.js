"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[2211],{

/***/ 2211:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(677);
/* harmony import */ var antd_es_typography__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5475);
/* harmony import */ var antd_es_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7355);
/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1850);
/* harmony import */ var antd_es_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3058);
/* harmony import */ var antd_es_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(2120);
/* harmony import */ var antd_es_space__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(8392);
/* harmony import */ var _ant_design_icons_es_icons_WifiOutlined__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(8165);
/* harmony import */ var _ant_design_icons_es_icons_SendOutlined__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(9290);
/* harmony import */ var _ant_design_icons_es_icons_ClearOutlined__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(1315);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }











var Title = antd_es_typography__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.Title,
  Text = antd_es_typography__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.Text;

/**
 * Simple WebSocket Page - Optimized for Bundle Size
 * Basic WebSocket testing functionality without heavy dependencies
 */
var SimpleWebSocketPage = function SimpleWebSocketPage() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    socket = _useState2[0],
    setSocket = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    connected = _useState4[0],
    setConnected = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    messages = _useState6[0],
    setMessages = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    inputMessage = _useState8[0],
    setInputMessage = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('ws://localhost:8000/ws/test/'),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    url = _useState0[0],
    setUrl = _useState0[1];

  // Connect to WebSocket
  var connect = function connect() {
    try {
      var ws = new WebSocket(url);
      ws.onopen = function () {
        setConnected(true);
        addMessage('Connected to WebSocket', 'system');
      };
      ws.onmessage = function (event) {
        addMessage(event.data, 'received');
      };
      ws.onclose = function () {
        setConnected(false);
        addMessage('Disconnected from WebSocket', 'system');
      };
      ws.onerror = function (error) {
        addMessage("Error: ".concat(error.message || 'Connection failed'), 'error');
      };
      setSocket(ws);
    } catch (error) {
      addMessage("Connection error: ".concat(error.message), 'error');
    }
  };

  // Disconnect from WebSocket
  var disconnect = function disconnect() {
    if (socket) {
      socket.close();
      setSocket(null);
    }
  };

  // Send message
  var sendMessage = function sendMessage() {
    if (socket && connected && inputMessage.trim()) {
      socket.send(inputMessage);
      addMessage(inputMessage, 'sent');
      setInputMessage('');
    }
  };

  // Add message to list
  var addMessage = function addMessage(content, type) {
    var message = {
      id: Date.now(),
      content: content,
      type: type,
      timestamp: new Date().toLocaleTimeString()
    };
    setMessages(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [message]);
    });
  };

  // Clear messages
  var clearMessages = function clearMessages() {
    setMessages([]);
  };

  // Handle Enter key
  var handleKeyPress = function handleKeyPress(e) {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  // Cleanup on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    return function () {
      if (socket) {
        socket.close();
      }
    };
  }, [socket]);
  var getMessageStyle = function getMessageStyle(type) {
    var baseStyle = {
      padding: '8px',
      marginBottom: '8px',
      borderRadius: '4px'
    };
    switch (type) {
      case 'sent':
        return _objectSpread(_objectSpread({}, baseStyle), {}, {
          backgroundColor: '#e6f7ff',
          borderLeft: '3px solid #1890ff'
        });
      case 'received':
        return _objectSpread(_objectSpread({}, baseStyle), {}, {
          backgroundColor: '#f6ffed',
          borderLeft: '3px solid #52c41a'
        });
      case 'error':
        return _objectSpread(_objectSpread({}, baseStyle), {}, {
          backgroundColor: '#fff2f0',
          borderLeft: '3px solid #ff4d4f'
        });
      case 'system':
        return _objectSpread(_objectSpread({}, baseStyle), {}, {
          backgroundColor: '#fafafa',
          borderLeft: '3px solid #d9d9d9'
        });
      default:
        return baseStyle;
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      padding: '24px',
      maxWidth: '800px',
      margin: '0 auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 2
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_WifiOutlined__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, null), " WebSocket Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    title: "Connection",
    style: {
      marginBottom: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    strong: true
  }, "WebSocket URL:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {
    value: url,
    onChange: function onChange(e) {
      return setUrl(e.target.value);
    },
    placeholder: "ws://localhost:8000/ws/test/",
    style: {
      marginTop: '4px'
    },
    disabled: connected
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, {
    type: "primary",
    onClick: connect,
    disabled: connected,
    loading: false
  }, "Connect"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, {
    onClick: disconnect,
    disabled: !connected
  }, "Disconnect"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_badge__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
    status: connected ? 'success' : 'error',
    text: connected ? 'Connected' : 'Disconnected'
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    title: "Send Message",
    style: {
      marginBottom: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A.Compact, {
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {
    value: inputMessage,
    onChange: function onChange(e) {
      return setInputMessage(e.target.value);
    },
    onKeyPress: handleKeyPress,
    placeholder: "Type your message...",
    disabled: !connected
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_SendOutlined__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .A, null),
    onClick: sendMessage,
    disabled: !connected || !inputMessage.trim()
  }, "Send"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "Messages (", messages.length, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, {
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_ClearOutlined__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A, null),
      onClick: clearMessages,
      disabled: messages.length === 0
    }, "Clear"))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      height: '300px',
      overflowY: 'auto',
      border: '1px solid #d9d9d9',
      borderRadius: '4px',
      padding: '8px'
    }
  }, messages.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      textAlign: 'center',
      color: '#999',
      padding: '40px',
      fontStyle: 'italic'
    }
  }, "No messages yet. Connect and send a message to get started.") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_list__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
    dataSource: messages,
    renderItem: function renderItem(message) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        key: message.id,
        style: getMessageStyle(message.type)
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '4px',
          fontSize: '12px',
          color: '#666'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
        style: {
          fontWeight: 'bold',
          textTransform: 'uppercase'
        }
      }, message.type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, message.timestamp)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          fontFamily: 'monospace',
          fontSize: '14px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }
      }, message.content));
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    title: "Help",
    size: "small",
    style: {
      marginTop: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "This is a simple WebSocket testing tool. Enter a WebSocket URL, connect, and start sending messages. The default URL connects to the local development server.")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleWebSocketPage);

/***/ })

}]);