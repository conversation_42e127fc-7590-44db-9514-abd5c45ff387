{"name": "frontend", "version": "1.0.0", "description": "Frontend application", "main": "index.js", "scripts": {"fix-npm": "npm cache clean --force && rm -rf node_modules package-lock.json && npm install", "fix-lint": "node fix-lint.js", "start": "webpack serve --mode development --port 3000 --host 0.0.0.0 --env PORT=3000", "build": "webpack --mode production", "build:modern": "cross-env BROWSERSLIST_ENV=modern webpack --mode production", "build:legacy": "cross-env BROWSERSLIST_ENV=legacy webpack --mode production", "build:analyze": "cross-env ANALYZE=true webpack --mode production", "build:prod": "node scripts/build-prod.js", "deploy:prod": "node scripts/deploy-prod.js", "analyze:webpack": "cross-env ANALYZE=true webpack --mode development", "imagemin": "imagemin-cli --out-dir=build/static/media build/static/media", "cssmin": "cleancss -o build/static/css/main.min.css build/static/css/main.css", "jsmin": "terser --compress --mangle --output build/static/js/main.min.js -- build/static/js/main.js", "lint": "eslint --ext .js,.jsx src", "lint:fix": "eslint --ext .js,.jsx src --fix", "test": "jest", "test:unit": "jest src/tests/unit", "test:integration": "jest src/tests/integration", "test:e2e": "jest src/tests/e2e", "test:homepage": "jest src/tests/e2e/homepage.loading.test.js", "test:performance": "jest src/tests/e2e/performance.test.js", "test:websocket": "jest src/tests/integration/websocket.connection.test.js", "test:service-worker": "jest src/tests/unit/service.worker.test.js", "test:all": "node src/tests/runAllTests.js", "test:coverage": "jest --coverage", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e:cypress": "start-server-and-test start http://localhost:3000 cypress:run", "test:e2e:cypress:open": "start-server-and-test start http://localhost:3000 cypress:open", "test:accessibility": "jest src/tests/unit/hooks/useAccessibility.test.js src/tests/unit/components/AccessibilityHelpers.test.js", "test:accessibility:axe": "jest src/tests/accessibility/axe.test.js", "test:ui": "jest src/tests/unit/components/StyledComponents.test.js", "test:websocket:integration": "jest src/tests/integration/WebSocketIntegration.test.js", "test:template:integration": "jest src/tests/template-integration.test.js", "test:security": "jest src/tests/security", "test:cross-browser": "playwright test", "test:visual": "playwright test --grep @visual", "test:performance:lighthouse": "node src/tests/performance/lighthouse.test.js", "test:bundle-size": "jest src/tests/performance/bundleSize.test.js", "bundle-size": "npm run build:analyze && node ./scripts/checkBundleSize.js", "analyze": "npm run build && npx source-map-explorer 'build/static/js/*.js'", "check-size": "node ./scripts/checkBundleSize.js", "analyze-bundle": "node scripts/analyzeBundleStructure.js", "monitor-bundle": "node scripts/bundleMonitor.js check", "watch-bundle": "node scripts/bundleMonitor.js watch", "serve": "node server.js", "test:unit:ci": "jest src/tests/unit --coverage --ci --testResultsProcessor=jest-sonar-reporter", "test:integration:ci": "jest src/tests/integration --coverage --ci", "test:e2e:ci": "playwright test --reporter=html,json,junit", "test:security:ci": "jest src/tests/security --ci", "test:accessibility:ci": "playwright test --project=accessibility --reporter=html,json", "test:performance:ci": "playwright test --project=performance --reporter=html,json", "test:cross-browser:ci": "playwright test --project=chromium,firefox,webkit --reporter=html,json", "test:all:ci": "npm run test:unit:ci && npm run test:integration:ci && npm run test:e2e:ci && npm run test:security:ci", "test:coverage:report": "npm run test:unit:ci && npm run test:integration:ci && node ../scripts/test-coverage-report.js", "lint:ci": "eslint --ext .js,.jsx src --format junit --output-file test-results/eslint-results.xml", "format:check": "prettier --check src/**/*.{js,jsx,json,css,md}", "format:write": "prettier --write src/**/*.{js,jsx,json,css,md}", "precommit": "npm run lint && npm run format:check && npm run test:unit", "prepush": "npm run test:all:ci"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@reduxjs/toolkit": "2.7.0", "antd": "^5.24.6", "axios": "1.9.0", "error-stack-parser": "^2.1.4", "process": "0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "6.1.0", "react-redux": "^9.1.0", "react-router-dom": "^6.30.0", "redux": "^5.0.1", "reselect": "4.1.8", "web-vitals": "^3.5.2"}, "overrides": {"ajv": "^8.17.1"}, "devDependencies": {"@axe-core/playwright": "^4.8.5", "@babel/core": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/runtime": "^7.23.9", "@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "assert": "2.1.0", "babel-loader": "^9.1.3", "babel-plugin-import": "1.13.8", "browserify-zlib": "0.2.0", "buffer": "6.0.3", "constants-browserify": "1.0.0", "core-js": "^3.36.0", "cross-env": "7.0.3", "crypto-browserify": "3.12.1", "css-loader": "^6.10.0", "cypress": "^13.6.2", "cypress-axe": "^1.5.0", "cypress-real-events": "^1.11.0", "eslint-plugin-cypress": "^2.15.1", "html-webpack-plugin": "^5.6.0", "https-browserify": "1.0.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "30.0.1", "jest-watch-typeahead": "3.0.1", "jest-websocket-mock": "^2.5.0", "lighthouse": "^11.4.0", "mini-css-extract-plugin": "^2.8.0", "msw": "^2.0.11", "node-polyfill-webpack-plugin": "4.1.0", "os-browserify": "0.3.0", "path-browserify": "1.0.1", "postcss-loader": "^8.1.0", "puppeteer": "^21.6.1", "querystring-es3": "0.2.1", "sass-loader": "^14.1.1", "source-map-explorer": "^2.5.3", "start-server-and-test": "^2.0.3", "stream-browserify": "3.0.0", "stream-http": "3.2.0", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.10", "tty-browserify": "0.0.1", "url": "0.11.4", "util": "0.12.5", "vm-browserify": "1.1.2", "webpack": "5.99.7", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "5.1.4", "webpack-dev-server": "4.15.2", "workbox-webpack-plugin": "^7.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/frontend.git"}, "keywords": ["frontend", "react", "application"], "bugs": {"url": "https://github.com/your-repo/frontend/issues"}, "author": "", "license": "ISC"}