"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1843],{

/***/ 1843:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  z: () => (/* binding */ isBright)
});

// UNUSED EXPORTS: default

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(6540);
// EXTERNAL MODULE: ./node_modules/@rc-component/color-picker/es/index.js + 14 modules
var es = __webpack_require__(1021);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(6942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var hooks_useMergedState = __webpack_require__(2533);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/RightOutlined.js + 1 modules
var RightOutlined = __webpack_require__(8000);
// EXTERNAL MODULE: ./node_modules/rc-collapse/es/index.js + 4 modules
var rc_collapse_es = __webpack_require__(1057);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Children/toArray.js
var toArray = __webpack_require__(2546);
// EXTERNAL MODULE: ./node_modules/rc-util/es/omit.js
var omit = __webpack_require__(9853);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/motion.js
var motion = __webpack_require__(3723);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/reactNode.js
var reactNode = __webpack_require__(682);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/warning.js
var warning = __webpack_require__(8877);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(2279);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useSize.js
var useSize = __webpack_require__(829);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 8 modules
var config_provider = __webpack_require__(8586);
;// ./node_modules/antd/es/collapse/CollapsePanel.js
"use client";






const CollapsePanel = /*#__PURE__*/react.forwardRef((props, ref) => {
  if (false) {}
  const {
    getPrefixCls
  } = react.useContext(config_provider/* ConfigContext */.QO);
  const {
    prefixCls: customizePrefixCls,
    className,
    showArrow = true
  } = props;
  const prefixCls = getPrefixCls('collapse', customizePrefixCls);
  const collapsePanelClassName = classnames_default()({
    [`${prefixCls}-no-arrow`]: !showArrow
  }, className);
  return /*#__PURE__*/react.createElement(rc_collapse_es/* default.Panel */.A.Panel, Object.assign({
    ref: ref
  }, props, {
    prefixCls: prefixCls,
    className: collapsePanelClassName
  }));
});
/* harmony default export */ const collapse_CollapsePanel = (CollapsePanel);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 9 modules
var cssinjs_es = __webpack_require__(978);
// EXTERNAL MODULE: ./node_modules/antd/es/style/index.js
var style = __webpack_require__(5905);
// EXTERNAL MODULE: ./node_modules/antd/es/style/motion/index.js + 6 modules
var style_motion = __webpack_require__(3435);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/internal.js + 3 modules
var internal = __webpack_require__(1113);
;// ./node_modules/antd/es/collapse/style/index.js




const genBaseStyle = token => {
  const {
    componentCls,
    contentBg,
    padding,
    headerBg,
    headerPadding,
    collapseHeaderPaddingSM,
    collapseHeaderPaddingLG,
    collapsePanelBorderRadius,
    lineWidth,
    lineType,
    colorBorder,
    colorText,
    colorTextHeading,
    colorTextDisabled,
    fontSizeLG,
    lineHeight,
    lineHeightLG,
    marginSM,
    paddingSM,
    paddingLG,
    paddingXS,
    motionDurationSlow,
    fontSizeIcon,
    contentPadding,
    fontHeight,
    fontHeightLG
  } = token;
  const borderBase = `${(0,cssinjs_es/* unit */.zA)(lineWidth)} ${lineType} ${colorBorder}`;
  return {
    [componentCls]: Object.assign(Object.assign({}, (0,style/* resetComponent */.dF)(token)), {
      backgroundColor: headerBg,
      border: borderBase,
      borderRadius: collapsePanelBorderRadius,
      '&-rtl': {
        direction: 'rtl'
      },
      [`& > ${componentCls}-item`]: {
        borderBottom: borderBase,
        '&:first-child': {
          [`
            &,
            & > ${componentCls}-header`]: {
            borderRadius: `${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} 0 0`
          }
        },
        '&:last-child': {
          [`
            &,
            & > ${componentCls}-header`]: {
            borderRadius: `0 0 ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)}`
          }
        },
        [`> ${componentCls}-header`]: Object.assign(Object.assign({
          position: 'relative',
          display: 'flex',
          flexWrap: 'nowrap',
          alignItems: 'flex-start',
          padding: headerPadding,
          color: colorTextHeading,
          lineHeight,
          cursor: 'pointer',
          transition: `all ${motionDurationSlow}, visibility 0s`
        }, (0,style/* genFocusStyle */.K8)(token)), {
          [`> ${componentCls}-header-text`]: {
            flex: 'auto'
          },
          // >>>>> Arrow
          [`${componentCls}-expand-icon`]: {
            height: fontHeight,
            display: 'flex',
            alignItems: 'center',
            paddingInlineEnd: marginSM
          },
          [`${componentCls}-arrow`]: Object.assign(Object.assign({}, (0,style/* resetIcon */.Nk)()), {
            fontSize: fontSizeIcon,
            // when `transform: rotate()` is applied to icon's root element
            transition: `transform ${motionDurationSlow}`,
            // when `transform: rotate()` is applied to icon's child element
            svg: {
              transition: `transform ${motionDurationSlow}`
            }
          }),
          // >>>>> Text
          [`${componentCls}-header-text`]: {
            marginInlineEnd: 'auto'
          }
        }),
        [`${componentCls}-collapsible-header`]: {
          cursor: 'default',
          [`${componentCls}-header-text`]: {
            flex: 'none',
            cursor: 'pointer'
          }
        },
        [`${componentCls}-collapsible-icon`]: {
          cursor: 'unset',
          [`${componentCls}-expand-icon`]: {
            cursor: 'pointer'
          }
        }
      },
      [`${componentCls}-content`]: {
        color: colorText,
        backgroundColor: contentBg,
        borderTop: borderBase,
        [`& > ${componentCls}-content-box`]: {
          padding: contentPadding
        },
        '&-hidden': {
          display: 'none'
        }
      },
      '&-small': {
        [`> ${componentCls}-item`]: {
          [`> ${componentCls}-header`]: {
            padding: collapseHeaderPaddingSM,
            paddingInlineStart: paddingXS,
            [`> ${componentCls}-expand-icon`]: {
              // Arrow offset
              marginInlineStart: token.calc(paddingSM).sub(paddingXS).equal()
            }
          },
          [`> ${componentCls}-content > ${componentCls}-content-box`]: {
            padding: paddingSM
          }
        }
      },
      '&-large': {
        [`> ${componentCls}-item`]: {
          fontSize: fontSizeLG,
          lineHeight: lineHeightLG,
          [`> ${componentCls}-header`]: {
            padding: collapseHeaderPaddingLG,
            paddingInlineStart: padding,
            [`> ${componentCls}-expand-icon`]: {
              height: fontHeightLG,
              // Arrow offset
              marginInlineStart: token.calc(paddingLG).sub(padding).equal()
            }
          },
          [`> ${componentCls}-content > ${componentCls}-content-box`]: {
            padding: paddingLG
          }
        }
      },
      [`${componentCls}-item:last-child`]: {
        borderBottom: 0,
        [`> ${componentCls}-content`]: {
          borderRadius: `0 0 ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)} ${(0,cssinjs_es/* unit */.zA)(collapsePanelBorderRadius)}`
        }
      },
      [`& ${componentCls}-item-disabled > ${componentCls}-header`]: {
        [`
          &,
          & > .arrow
        `]: {
          color: colorTextDisabled,
          cursor: 'not-allowed'
        }
      },
      // ========================== Icon Position ==========================
      [`&${componentCls}-icon-position-end`]: {
        [`& > ${componentCls}-item`]: {
          [`> ${componentCls}-header`]: {
            [`${componentCls}-expand-icon`]: {
              order: 1,
              paddingInlineEnd: 0,
              paddingInlineStart: marginSM
            }
          }
        }
      }
    })
  };
};
const genArrowStyle = token => {
  const {
    componentCls
  } = token;
  const fixedSelector = `> ${componentCls}-item > ${componentCls}-header ${componentCls}-arrow`;
  return {
    [`${componentCls}-rtl`]: {
      [fixedSelector]: {
        transform: `rotate(180deg)`
      }
    }
  };
};
const genBorderlessStyle = token => {
  const {
    componentCls,
    headerBg,
    borderlessContentPadding,
    borderlessContentBg,
    colorBorder
  } = token;
  return {
    [`${componentCls}-borderless`]: {
      backgroundColor: headerBg,
      border: 0,
      [`> ${componentCls}-item`]: {
        borderBottom: `1px solid ${colorBorder}`
      },
      [`
        > ${componentCls}-item:last-child,
        > ${componentCls}-item:last-child ${componentCls}-header
      `]: {
        borderRadius: 0
      },
      [`> ${componentCls}-item:last-child`]: {
        borderBottom: 0
      },
      [`> ${componentCls}-item > ${componentCls}-content`]: {
        backgroundColor: borderlessContentBg,
        borderTop: 0
      },
      [`> ${componentCls}-item > ${componentCls}-content > ${componentCls}-content-box`]: {
        padding: borderlessContentPadding
      }
    }
  };
};
const genGhostStyle = token => {
  const {
    componentCls,
    paddingSM
  } = token;
  return {
    [`${componentCls}-ghost`]: {
      backgroundColor: 'transparent',
      border: 0,
      [`> ${componentCls}-item`]: {
        borderBottom: 0,
        [`> ${componentCls}-content`]: {
          backgroundColor: 'transparent',
          border: 0,
          [`> ${componentCls}-content-box`]: {
            paddingBlock: paddingSM
          }
        }
      }
    }
  };
};
const prepareComponentToken = token => ({
  headerPadding: `${token.paddingSM}px ${token.padding}px`,
  headerBg: token.colorFillAlter,
  contentPadding: `${token.padding}px 16px`,
  // Fixed Value
  contentBg: token.colorBgContainer,
  borderlessContentPadding: `${token.paddingXXS}px 16px ${token.padding}px`,
  borderlessContentBg: 'transparent'
});
/* harmony default export */ const collapse_style = ((0,internal/* genStyleHooks */.OF)('Collapse', token => {
  const collapseToken = (0,internal/* mergeToken */.oX)(token, {
    collapseHeaderPaddingSM: `${(0,cssinjs_es/* unit */.zA)(token.paddingXS)} ${(0,cssinjs_es/* unit */.zA)(token.paddingSM)}`,
    collapseHeaderPaddingLG: `${(0,cssinjs_es/* unit */.zA)(token.padding)} ${(0,cssinjs_es/* unit */.zA)(token.paddingLG)}`,
    collapsePanelBorderRadius: token.borderRadiusLG
  });
  return [genBaseStyle(collapseToken), genBorderlessStyle(collapseToken), genGhostStyle(collapseToken), genArrowStyle(collapseToken), (0,style_motion/* genCollapseMotion */.eG)(collapseToken)];
}, prepareComponentToken));
;// ./node_modules/antd/es/collapse/Collapse.js
"use client";














const Collapse_Collapse = /*#__PURE__*/react.forwardRef((props, ref) => {
  const {
    getPrefixCls,
    direction,
    expandIcon: contextExpandIcon,
    className: contextClassName,
    style: contextStyle
  } = (0,context/* useComponentConfig */.TP)('collapse');
  const {
    prefixCls: customizePrefixCls,
    className,
    rootClassName,
    style,
    bordered = true,
    ghost,
    size: customizeSize,
    expandIconPosition = 'start',
    children,
    destroyInactivePanel,
    destroyOnHidden,
    expandIcon
  } = props;
  const mergedSize = (0,useSize/* default */.A)(ctx => {
    var _a;
    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : ctx) !== null && _a !== void 0 ? _a : 'middle';
  });
  const prefixCls = getPrefixCls('collapse', customizePrefixCls);
  const rootPrefixCls = getPrefixCls();
  const [wrapCSSVar, hashId, cssVarCls] = collapse_style(prefixCls);
  if (false) {}
  // Align with logic position
  const mergedExpandIconPosition = react.useMemo(() => {
    if (expandIconPosition === 'left') {
      return 'start';
    }
    return expandIconPosition === 'right' ? 'end' : expandIconPosition;
  }, [expandIconPosition]);
  const mergedExpandIcon = expandIcon !== null && expandIcon !== void 0 ? expandIcon : contextExpandIcon;
  const renderExpandIcon = react.useCallback((panelProps = {}) => {
    const icon = typeof mergedExpandIcon === 'function' ? mergedExpandIcon(panelProps) : (/*#__PURE__*/react.createElement(RightOutlined/* default */.A, {
      rotate: panelProps.isActive ? direction === 'rtl' ? -90 : 90 : undefined,
      "aria-label": panelProps.isActive ? 'expanded' : 'collapsed'
    }));
    return (0,reactNode/* cloneElement */.Ob)(icon, () => {
      var _a;
      return {
        className: classnames_default()((_a = icon === null || icon === void 0 ? void 0 : icon.props) === null || _a === void 0 ? void 0 : _a.className, `${prefixCls}-arrow`)
      };
    });
  }, [mergedExpandIcon, prefixCls]);
  const collapseClassName = classnames_default()(`${prefixCls}-icon-position-${mergedExpandIconPosition}`, {
    [`${prefixCls}-borderless`]: !bordered,
    [`${prefixCls}-rtl`]: direction === 'rtl',
    [`${prefixCls}-ghost`]: !!ghost,
    [`${prefixCls}-${mergedSize}`]: mergedSize !== 'middle'
  }, contextClassName, className, rootClassName, hashId, cssVarCls);
  const openMotion = Object.assign(Object.assign({}, (0,motion/* default */.A)(rootPrefixCls)), {
    motionAppear: false,
    leavedClassName: `${prefixCls}-content-hidden`
  });
  const items = react.useMemo(() => {
    if (children) {
      return (0,toArray/* default */.A)(children).map((child, index) => {
        var _a, _b;
        const childProps = child.props;
        if (childProps === null || childProps === void 0 ? void 0 : childProps.disabled) {
          const key = (_a = child.key) !== null && _a !== void 0 ? _a : String(index);
          const mergedChildProps = Object.assign(Object.assign({}, (0,omit/* default */.A)(child.props, ['disabled'])), {
            key,
            collapsible: (_b = childProps.collapsible) !== null && _b !== void 0 ? _b : 'disabled'
          });
          return (0,reactNode/* cloneElement */.Ob)(child, mergedChildProps);
        }
        return child;
      });
    }
    return null;
  }, [children]);
  return wrapCSSVar(
  /*#__PURE__*/
  // @ts-ignore
  react.createElement(rc_collapse_es/* default */.A, Object.assign({
    ref: ref,
    openMotion: openMotion
  }, (0,omit/* default */.A)(props, ['rootClassName']), {
    expandIcon: renderExpandIcon,
    prefixCls: prefixCls,
    className: collapseClassName,
    style: Object.assign(Object.assign({}, contextStyle), style),
    // TODO: In the future, destroyInactivePanel in rc-collapse needs to be upgrade to destroyOnHidden
    destroyInactivePanel: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyInactivePanel
  }), items));
});
if (false) {}
/* harmony default export */ const collapse_Collapse = (Object.assign(Collapse_Collapse, {
  Panel: collapse_CollapsePanel
}));
;// ./node_modules/antd/es/collapse/index.js
"use client";


/* harmony default export */ const collapse = ((/* unused pure expression or super */ null && (Collapse)));
// EXTERNAL MODULE: ./node_modules/antd/es/locale/index.js + 1 modules
var locale = __webpack_require__(2277);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(436);
// EXTERNAL MODULE: ./node_modules/antd/es/color-picker/color.js
var color = __webpack_require__(9911);
;// ./node_modules/antd/es/color-picker/util.js



const util_generateColor = color => {
  if (color instanceof AggregationColor) {
    return color;
  }
  return new AggregationColor(color);
};
const getRoundNumber = value => Math.round(Number(value || 0));
const getColorAlpha = color => getRoundNumber(color.toHsb().a * 100);
/** Return the color whose `alpha` is 1 */
const genAlphaColor = (color, alpha) => {
  const rgba = color.toRgb();
  // Color from hsb input may get `rgb` is (0/0/0) when `hsb.b` is 0
  // So if rgb is empty, we should get from hsb
  if (!rgba.r && !rgba.g && !rgba.b) {
    const hsba = color.toHsb();
    hsba.a = alpha || 1;
    return util_generateColor(hsba);
  }
  rgba.a = alpha || 1;
  return util_generateColor(rgba);
};
/**
 * Get percent position color. e.g. [10%-#fff, 20%-#000], 15% => #888
 */
const getGradientPercentColor = (colors, percent) => {
  const filledColors = [{
    percent: 0,
    color: colors[0].color
  }].concat(_toConsumableArray(colors), [{
    percent: 100,
    color: colors[colors.length - 1].color
  }]);
  for (let i = 0; i < filledColors.length - 1; i += 1) {
    const startPtg = filledColors[i].percent;
    const endPtg = filledColors[i + 1].percent;
    const startColor = filledColors[i].color;
    const endColor = filledColors[i + 1].color;
    if (startPtg <= percent && percent <= endPtg) {
      const dist = endPtg - startPtg;
      if (dist === 0) {
        return startColor;
      }
      const ratio = (percent - startPtg) / dist * 100;
      const startRcColor = new RcColor(startColor);
      const endRcColor = new RcColor(endColor);
      return startRcColor.mix(endRcColor, ratio).toRgbString();
    }
  }
  // This will never reach
  /* istanbul ignore next */
  return '';
};
;// ./node_modules/antd/es/color-picker/components/ColorPresets.js
"use client";









const genPresetColor = list => list.map(value => {
  value.colors = value.colors.map(generateColor);
  return value;
});
const isBright = (value, bgColorToken) => {
  const {
    r,
    g,
    b,
    a
  } = value.toRgb();
  const hsv = new es/* Color */.Q1(value.toRgbString()).onBackground(bgColorToken).toHsv();
  if (a <= 0.5) {
    // Adapted to dark mode
    return hsv.v > 0.5;
  }
  return r * 0.299 + g * 0.587 + b * 0.114 > 192;
};
const genCollapsePanelKey = (preset, index) => {
  var _a;
  const mergedKey = (_a = preset.key) !== null && _a !== void 0 ? _a : index;
  return `panel-${mergedKey}`;
};
const ColorPresets = ({
  prefixCls,
  presets,
  value: color,
  onChange
}) => {
  const [locale] = useLocale('ColorPicker');
  const [, token] = useToken();
  const [presetsValue] = useMergedState(genPresetColor(presets), {
    value: genPresetColor(presets),
    postState: genPresetColor
  });
  const colorPresetsPrefixCls = `${prefixCls}-presets`;
  const activeKeys = useMemo(() => presetsValue.reduce((acc, preset, index) => {
    const {
      defaultOpen = true
    } = preset;
    if (defaultOpen) {
      acc.push(genCollapsePanelKey(preset, index));
    }
    return acc;
  }, []), [presetsValue]);
  const handleClick = colorValue => {
    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);
  };
  const items = presetsValue.map((preset, index) => {
    var _a;
    return {
      key: genCollapsePanelKey(preset, index),
      label: /*#__PURE__*/React.createElement("div", {
        className: `${colorPresetsPrefixCls}-label`
      }, preset === null || preset === void 0 ? void 0 : preset.label),
      children: (/*#__PURE__*/React.createElement("div", {
        className: `${colorPresetsPrefixCls}-items`
      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map((presetColor, index) => (/*#__PURE__*/React.createElement(ColorBlock
      // eslint-disable-next-line react/no-array-index-key
      , {
        // eslint-disable-next-line react/no-array-index-key
        key: `preset-${index}-${presetColor.toHexString()}`,
        color: generateColor(presetColor).toRgbString(),
        prefixCls: prefixCls,
        className: classNames(`${colorPresetsPrefixCls}-color`, {
          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),
          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, token.colorBgElevated)
        }),
        onClick: () => handleClick(presetColor)
      }))) : (/*#__PURE__*/React.createElement("span", {
        className: `${colorPresetsPrefixCls}-empty`
      }, locale.presetEmpty))))
    };
  });
  return /*#__PURE__*/React.createElement("div", {
    className: colorPresetsPrefixCls
  }, /*#__PURE__*/React.createElement(Collapse, {
    defaultActiveKey: activeKeys,
    ghost: true,
    items: items
  }));
};
/* harmony default export */ const components_ColorPresets = ((/* unused pure expression or super */ null && (ColorPresets)));

/***/ }),

/***/ 9911:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   kf: () => (/* binding */ AggregationColor)
/* harmony export */ });
/* unused harmony exports toHexFormat, getHex */
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3029);
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2901);
/* harmony import */ var _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1021);



const toHexFormat = (value, alpha) => (value === null || value === void 0 ? void 0 : value.replace(/[^\w/]/g, '').slice(0, alpha ? 8 : 6)) || '';
const getHex = (value, alpha) => value ? toHexFormat(value, alpha) : '';
let AggregationColor = /*#__PURE__*/function () {
  function AggregationColor(color) {
    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this, AggregationColor);
    var _a;
    this.cleared = false;
    // Clone from another AggregationColor
    if (color instanceof AggregationColor) {
      this.metaColor = color.metaColor.clone();
      this.colors = (_a = color.colors) === null || _a === void 0 ? void 0 : _a.map(info => ({
        color: new AggregationColor(info.color),
        percent: info.percent
      }));
      this.cleared = color.cleared;
      return;
    }
    const isArray = Array.isArray(color);
    if (isArray && color.length) {
      this.colors = color.map(({
        color: c,
        percent
      }) => ({
        color: new AggregationColor(c),
        percent
      }));
      this.metaColor = new _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_2__/* .Color */ .Q1(this.colors[0].color.metaColor);
    } else {
      this.metaColor = new _rc_component_color_picker__WEBPACK_IMPORTED_MODULE_2__/* .Color */ .Q1(isArray ? '' : color);
    }
    if (!color || isArray && !this.colors) {
      this.metaColor = this.metaColor.setA(0);
      this.cleared = true;
    }
  }
  return (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(AggregationColor, [{
    key: "toHsb",
    value: function toHsb() {
      return this.metaColor.toHsb();
    }
  }, {
    key: "toHsbString",
    value: function toHsbString() {
      return this.metaColor.toHsbString();
    }
  }, {
    key: "toHex",
    value: function toHex() {
      return getHex(this.toHexString(), this.metaColor.a < 1);
    }
  }, {
    key: "toHexString",
    value: function toHexString() {
      return this.metaColor.toHexString();
    }
  }, {
    key: "toRgb",
    value: function toRgb() {
      return this.metaColor.toRgb();
    }
  }, {
    key: "toRgbString",
    value: function toRgbString() {
      return this.metaColor.toRgbString();
    }
  }, {
    key: "isGradient",
    value: function isGradient() {
      return !!this.colors && !this.cleared;
    }
  }, {
    key: "getColors",
    value: function getColors() {
      return this.colors || [{
        color: this,
        percent: 0
      }];
    }
  }, {
    key: "toCssString",
    value: function toCssString() {
      const {
        colors
      } = this;
      // CSS line-gradient
      if (colors) {
        const colorsStr = colors.map(c => `${c.color.toRgbString()} ${c.percent}%`).join(', ');
        return `linear-gradient(90deg, ${colorsStr})`;
      }
      return this.metaColor.toRgbString();
    }
  }, {
    key: "equals",
    value: function equals(color) {
      if (!color || this.isGradient() !== color.isGradient()) {
        return false;
      }
      if (!this.isGradient()) {
        return this.toHexString() === color.toHexString();
      }
      return this.colors.length === color.colors.length && this.colors.every((c, i) => {
        const target = color.colors[i];
        return c.percent === target.percent && c.color.equals(target.color);
      });
    }
  }]);
}();

/***/ })

}]);