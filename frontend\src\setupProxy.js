const { createProxyMiddleware } = require('http-proxy-middleware');

// Determine backend URL with fallback logic
const backendUrl = process.env.REACT_APP_WS_PROXY_TARGET ||
  (process.env.REACT_APP_BACKEND_HOST ? `http://${process.env.REACT_APP_BACKEND_HOST}:8000` : 'http://localhost:8000');

module.exports = function (app) {
  console.log('🔧 Setting up proxy middleware for real backend connections');
  console.log('🌐 Backend URL:', backendUrl);
  console.log('🔌 WebSocket proxy will route /ws to backend WebSocket server');

  // API proxy for regular HTTP requests
  app.use(
    '/api',
    createProxyMiddleware({
      target: backendUrl,
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      onProxyReq: (_proxyReq, req, _res) => {
        // Log proxy requests for debugging
        console.log(`Proxying API request: ${req.method} ${req.url} -> ${backendUrl}${req.url}`);
      },
      onError: (err, _req, res) => {
        console.error('API proxy error:', err);
        if (res && !res.headersSent) {
          res.writeHead(502, {
            'Content-Type': 'application/json',
          });
          res.end(JSON.stringify({
            error: 'Backend service unavailable',
            message: 'The backend service is currently unavailable. Please try again later.'
          }));
        }
      }
    })
  );

  // Enable WebSocket proxy for development - this routes to real backend WebSocket server
  // Use /api/ws to avoid conflicts with webpack dev server's WebSocket
  console.log('🔌 WebSocket proxy enabled. Proxying /api/ws to', backendUrl + '/ws');
  app.use(
    '/api/ws',
    createProxyMiddleware({
      target: backendUrl,
      changeOrigin: true,
      ws: true, // Enable WebSocket proxying
      logLevel: 'debug',
      secure: false,
      pathRewrite: {
        '^/api/ws': '/ws' // Rewrite /api/ws to /ws for backend
      },
      onProxyReq: (_proxyReq, req, _res) => {
        console.log(`Proxying WebSocket request: ${req.url} -> ${backendUrl}/ws`);
      },
      onError: (err, _req, res) => {
        console.error('WebSocket proxy error:', err);
        if (res && res.writeHead) {
          res.writeHead(502, {
            'Content-Type': 'text/plain',
          });
          res.end(`WebSocket proxy error: ${err.message}`);
        }
      }
    })
  );
};
