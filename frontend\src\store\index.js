import { configureStore } from '@reduxjs/toolkit';
import { combineReducers } from 'redux';

// Import all reducers from the redux directory
import appReducer from '../redux/reducers/appReducer';
import websocketReducer from '../redux/reducers/websocketReducer';
import appDataReducer from '../redux/reducers/appDataReducer';
import webSocketClientReducer from '../redux/reducers/webSocketClientReducer';
import uiReducer from '../redux/reducers/uiReducer';
import apiKeysReducer from '../redux/reducers/apiKeysReducer';
import networkReducer from '../redux/reducers/networkReducer';
import themeReducer from '../redux/reducers/themeReducer';
import userReducer from '../redux/reducers/userReducer';
import projectReducer from '../redux/reducers/projectReducer';

// Import middleware
import websocketMiddleware from '../redux/middleware/websocketMiddleware';

// Performance monitoring middleware
const performanceMiddleware = store => next => action => {
  const start = performance.now();
  const result = next(action);
  const duration = performance.now() - start;

  // Log slow actions (> 10ms)
  if (duration > 10) {
    console.warn(`Slow Redux action: ${action.type} took ${duration.toFixed(2)}ms`);
  }

  // Emit a custom event for the performance monitor
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('redux-action', {
      detail: {
        type: action.type,
        duration,
        timestamp: new Date().toISOString(),
        state: store.getState()
      }
    }));
  }

  return result;
};

// Import the new App Builder reducer
import appBuilderReducer from '../redux/reducers/appBuilderReducer';

// Combine all reducers
const rootReducer = combineReducers({
  app: appReducer,
  websocket: websocketReducer,
  appData: appDataReducer,
  webSocketClient: webSocketClientReducer,
  ui: uiReducer,
  apiKeys: apiKeysReducer,
  network: networkReducer,
  themes: themeReducer,
  user: userReducer,
  projects: projectReducer,
  appBuilder: appBuilderReducer
});

/**
 * Configure the unified Redux store
 */
const createAppStore = (preloadedState = {}) => {
  // Ensure websocket state is always available
  const defaultState = {
    websocket: {
      connected: false,
      connecting: false,
      error: null,
      lastMessage: null,
      messages: [],
      status: 'disconnected',
      url: null,
      reconnectAttempt: 0,
      closeInfo: null,
      willReconnect: false
    },
    ...preloadedState
  };

  return configureStore({
    reducer: rootReducer,
    preloadedState: defaultState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          // Ignore these action types in serializability checks
          ignoredActions: [
            'WS_MESSAGE_RECEIVED',
            'WS_ERROR',
            'WEBSOCKET_MESSAGE_RECEIVED',
            'WEBSOCKET_ERROR',
            'theme/addTheme',
            'theme/updateTheme',
          ],
          // Ignore these field paths in serializability checks
          ignoredPaths: [
            'websocket.socket',
            'error.originalError',
            'webSocketClient.socket',
            'theme.themes'
          ]
        },
        thunk: true
      }).concat(websocketMiddleware(), performanceMiddleware),
    // Enable Redux DevTools integration
    devTools: {
      name: 'App Builder 201',
      trace: true,
      traceLimit: 25
    }
  });
};

// Create and export the store instance
const store = createAppStore();

export default store;
export { createAppStore };