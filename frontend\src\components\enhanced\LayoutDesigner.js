import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { createSelector } from 'reselect';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  CloseOutlined,
  DragOutlined,
  <PERSON>Outlined,
  <PERSON>umnWidthOutlined,
  ColumnHeightOutlined,
  BorderOutlined
} from '@ant-design/icons';

// Import fallback component
import BasicLayoutDesigner from './BasicLayoutDesigner';

// Import the new unified actions and selectors
import {
  addLayout,
  updateLayout,
  deleteLayout,
  addComponentToLayout,
  removeComponentFromLayout,
  moveComponentInLayout,
  setSelectedLayout
} from '../../redux/actions/appBuilderActions';
import {
  getComponents,
  getLayouts,
  getSelectedLayout,
  getLayoutWithComponents
} from '../../redux/selectors/appBuilderSelectors';

// Import the enhanced drag-and-drop system
import { DropZone, DragPreview } from '../shared/DragDropSystem';

// Try to import Redux actions with fallback
let fallbackAddLayout, fallbackUpdateLayout, fallbackRemoveLayout;
try {
  const actions = require('../../redux/minimal-store');
  fallbackAddLayout = actions.addLayout;
  fallbackUpdateLayout = actions.updateLayout;
  fallbackRemoveLayout = actions.removeLayout;
} catch (error) {
  console.warn('Redux actions not available, using fallback');
  fallbackAddLayout = () => ({ type: 'ADD_LAYOUT' });
  fallbackUpdateLayout = () => ({ type: 'UPDATE_LAYOUT' });
  fallbackRemoveLayout = () => ({ type: 'REMOVE_LAYOUT' });
}

// Try to import design system with fallback
let styled, Button, Card, Input, Select, theme;
let hasDesignSystem = true;
try {
  const designSystem = require('../../design-system');
  styled = designSystem.styled;
  Button = designSystem.Button;
  Card = designSystem.Card;
  Input = designSystem.Input;
  Select = designSystem.Select;
  theme = require('../../design-system/theme').default;
} catch (error) {
  console.warn('Design system not available, using fallback');
  hasDesignSystem = false;
  // Provide fallback theme
  theme = {
    spacing: ['0', '4px', '8px', '12px', '16px', '20px', '24px', '32px', '48px'],
    colors: {
      primary: { main: '#1976d2', light: '#e3f2fd' },
      neutral: { 100: '#f5f5f5', 300: '#e0e0e0', 400: '#bdbdbd', 500: '#9e9e9e' },
      danger: { main: '#d32f2f' }
    },
    borderRadius: { md: '4px' },
    typography: {
      fontWeight: { medium: 500, semibold: 600 },
      fontSize: { sm: '14px' }
    }
  };
}

// Define styled components with fallback support
let LayoutDesignerContainer, LayoutGrid, LayoutCanvas, LayoutItem, ComponentPalette, PaletteItem, PropertyEditor, PropertyGroup, GridControls, EmptyState;

if (hasDesignSystem && styled) {
  LayoutDesignerContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: ${theme.spacing[4]};
  `;

  LayoutGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: ${theme.spacing[4]};
  `;

  LayoutCanvas = styled.div`
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(12, 40px);
    gap: ${theme.spacing[2]};
    background-color: ${theme.colors.neutral[100]};
    border: 1px dashed ${theme.colors.neutral[300]};
    border-radius: ${theme.borderRadius.md};
    padding: ${theme.spacing[4]};
    min-height: 500px;
  `;

  LayoutItem = styled.div`
    grid-column: span ${props => props.width || 3};
    grid-row: span ${props => props.height || 2};
    background-color: ${props => props.isPlaceholder ? theme.colors.primary.light : 'white'};
    border: 1px solid ${props => props.isSelected ? theme.colors.primary.main : theme.colors.neutral[300]};
    border-radius: ${theme.borderRadius.md};
    padding: ${theme.spacing[2]};
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: ${props => props.isDragging ? 'grabbing' : 'grab'};
    opacity: ${props => props.isDragging ? 0.5 : 1};
    box-shadow: ${props => props.isSelected ? `0 0 0 2px ${theme.colors.primary.main}` : 'none'};
  `;

  ComponentPalette = styled.div`
    display: flex;
    flex-wrap: wrap;
    gap: ${theme.spacing[2]};
    margin-bottom: ${theme.spacing[4]};
  `;

  PaletteItem = styled.div`
    padding: ${theme.spacing[2]};
    background-color: white;
    border: 1px solid ${theme.colors.neutral[300]};
    border-radius: ${theme.borderRadius.md};
    cursor: grab;
    display: flex;
    align-items: center;
    gap: ${theme.spacing[2]};

    &:hover {
      background-color: ${theme.colors.neutral[100]};
    }
  `;

  PropertyEditor = styled.div`
    display: flex;
    flex-direction: column;
    gap: ${theme.spacing[3]};
  `;

  PropertyGroup = styled.div`
    display: flex;
    flex-direction: column;
    gap: ${theme.spacing[2]};
  `;

  GridControls = styled.div`
    display: flex;
    gap: ${theme.spacing[2]};
    margin-bottom: ${theme.spacing[4]};
  `;

  EmptyState = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: ${theme.spacing[8]};
    background-color: ${theme.colors.neutral[100]};
    border-radius: ${theme.borderRadius.md};
    text-align: center;
  `;
} else {
  // Fallback to regular div components
  LayoutDesignerContainer = ({ children, ...props }) => <div {...props}>{children}</div>;
  LayoutGrid = ({ children, ...props }) => <div {...props}>{children}</div>;
  LayoutCanvas = ({ children, ...props }) => <div {...props}>{children}</div>;
  LayoutItem = ({ children, ...props }) => <div {...props}>{children}</div>;
  ComponentPalette = ({ children, ...props }) => <div {...props}>{children}</div>;
  PaletteItem = ({ children, ...props }) => <div {...props}>{children}</div>;
  PropertyEditor = ({ children, ...props }) => <div {...props}>{children}</div>;
  PropertyGroup = ({ children, ...props }) => <div {...props}>{children}</div>;
  GridControls = ({ children, ...props }) => <div {...props}>{children}</div>;
  EmptyState = ({ children, ...props }) => <div {...props}>{children}</div>;
}

// Component Item with drag-and-drop functionality
const DraggableComponent = ({ component, index, onSelect, isSelected, onRemove, onDragStart, onDragEnd }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: component.x || 0, y: component.y || 0 });

  // Handle drag start
  const handleDragStart = (e) => {
    setIsDragging(true);
    if (onDragStart) onDragStart(component);

    // Store the initial mouse position and component position
    const initialMousePos = { x: e.clientX, y: e.clientY };
    const initialCompPos = { x: position.x, y: position.y };

    // Handle mouse move
    const handleMouseMove = (e) => {
      const dx = e.clientX - initialMousePos.x;
      const dy = e.clientY - initialMousePos.y;

      // Calculate new position (this would be grid-based in a real implementation)
      const newX = Math.max(0, initialCompPos.x + Math.round(dx / 50));
      const newY = Math.max(0, initialCompPos.y + Math.round(dy / 40));

      setPosition({ x: newX, y: newY });
    };

    // Handle mouse up
    const handleMouseUp = () => {
      setIsDragging(false);
      if (onDragEnd) onDragEnd(component, position);

      // Remove event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <LayoutItem
      isDragging={isDragging}
      isSelected={isSelected}
      width={component.width}
      height={component.height}
      style={{
        gridColumn: `${position.x + 1} / span ${component.width}`,
        gridRow: `${position.y + 1} / span ${component.height}`,
        cursor: isDragging ? 'grabbing' : 'grab',
        position: 'relative',
        zIndex: isDragging ? 10 : 1
      }}
      onClick={(e) => {
        e.stopPropagation();
        onSelect(component);
      }}
      onMouseDown={handleDragStart}
    >
      <div>
        <div style={{ fontWeight: theme.typography.fontWeight.semibold }}>{component.name}</div>
        <div style={{ fontSize: theme.typography.fontSize.sm, color: theme.colors.neutral[500] }}>
          {component.type}
        </div>
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ fontSize: theme.typography.fontSize.sm, color: theme.colors.neutral[500] }}>
          {component.width}x{component.height}
        </div>
        <div style={{ display: 'flex', gap: '4px' }}>
          <DragOutlined style={{ cursor: 'grab' }} />
          <DeleteOutlined
            style={{ cursor: 'pointer', color: theme.colors.danger.main }}
            onClick={(e) => {
              e.stopPropagation();
              if (onRemove) onRemove(component.id);
            }}
          />
        </div>
      </div>
    </LayoutItem>
  );
};

const DropTarget = ({ onDrop, children }) => {
  const [isOver, setIsOver] = useState(false);
  const dropRef = React.useRef(null);

  // Handle drag over
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsOver(true);
  };

  // Handle drag leave
  const handleDragLeave = () => {
    setIsOver(false);
  };

  // Handle drop
  const handleDrop = (e) => {
    e.preventDefault();
    setIsOver(false);

    // Get drop position relative to the drop target
    const rect = dropRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Calculate grid position
    const gridX = Math.floor(x / 50);
    const gridY = Math.floor(y / 40);

    // Call the onDrop callback with the component data and position
    if (onDrop) {
      try {
        const data = JSON.parse(e.dataTransfer.getData('application/json'));
        onDrop(data, { x: gridX, y: gridY });
      } catch (error) {
        console.error('Error parsing drag data:', error);
      }
    }
  };

  // Handle click on the canvas (deselect items)
  const handleCanvasClick = () => {
    // This would typically call a function to deselect any selected items
  };

  return (
    <div
      ref={dropRef}
      style={{ position: 'relative', height: '100%' }}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleCanvasClick}
    >
      {children}
      {isOver && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            border: `2px dashed ${theme.colors.primary.main}`,
            borderRadius: theme.borderRadius.md,
            zIndex: 10,
          }}
        />
      )}
    </div>
  );
};

// Memoized selectors to prevent unnecessary re-renders
const getAppState = (state) => state.app || {};
const getAppDataState = (state) => state.appData || {};

const selectLayoutComponents = createSelector(
  [getAppState, getAppDataState],
  (app, appData) => app.components || appData.components || []
);

const selectLayoutLayouts = createSelector(
  [getAppState, getAppDataState],
  (app, appData) => app.layouts || appData.layouts || []
);

// Main wrapper component with error handling
const LayoutDesigner = () => {
  // Check if we have all required dependencies
  const hasRequiredDeps = hasDesignSystem && styled && Button && Card && Input && Select && theme;

  if (!hasRequiredDeps) {
    console.log('Using basic layout designer due to missing dependencies');
    return <BasicLayoutDesigner />;
  }

  // If we have all dependencies, use the enhanced version
  return <EnhancedLayoutDesigner />;
};

const EnhancedLayoutDesigner = () => {
  const dispatch = useDispatch();

  // Use the new unified selectors with fallback
  let components, layouts, selectedLayoutFromStore;
  try {
    components = useSelector(getComponents);
    layouts = useSelector(getLayouts);
    selectedLayoutFromStore = useSelector(getSelectedLayout);
  } catch (error) {
    console.warn('Using fallback selectors');
    components = useSelector(selectLayoutComponents);
    layouts = useSelector(selectLayoutLayouts);
    selectedLayoutFromStore = null;
  }

  const [layoutName, setLayoutName] = useState('');
  const [layoutType, setLayoutType] = useState('grid');
  const [layoutItems, setLayoutItems] = useState([]);
  const [selectedLayout, setSelectedLayout] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [errors, setErrors] = useState({});

  // Item properties
  const [itemWidth, setItemWidth] = useState(3);
  const [itemHeight, setItemHeight] = useState(2);

  const layoutTypes = [
    { value: 'grid', label: 'Grid Layout', description: 'Responsive grid system with columns' },
    { value: 'flex', label: 'Flex Layout', description: 'Flexible box layout for dynamic content' },
    { value: 'stack', label: 'Stack Layout', description: 'Vertical stacking of components' },
    { value: 'masonry', label: 'Masonry Layout', description: 'Pinterest-style masonry grid' },
    { value: 'sidebar', label: 'Sidebar Layout', description: 'Main content with sidebar' },
    { value: 'hero', label: 'Hero Layout', description: 'Large hero section with content below' },
    { value: 'custom', label: 'Custom Layout', description: 'Custom positioning and styling' }
  ];

  // Layout tools and controls
  const layoutTools = [
    { id: 'align-left', label: 'Align Left', icon: 'AlignLeftOutlined' },
    { id: 'align-center', label: 'Align Center', icon: 'AlignCenterOutlined' },
    { id: 'align-right', label: 'Align Right', icon: 'AlignRightOutlined' },
    { id: 'distribute-horizontal', label: 'Distribute Horizontally', icon: 'ColumnWidthOutlined' },
    { id: 'distribute-vertical', label: 'Distribute Vertically', icon: 'ColumnHeightOutlined' },
    { id: 'group', label: 'Group Items', icon: 'GroupOutlined' },
    { id: 'ungroup', label: 'Ungroup Items', icon: 'UngroupOutlined' },
    { id: 'bring-forward', label: 'Bring Forward', icon: 'VerticalAlignTopOutlined' },
    { id: 'send-backward', label: 'Send Backward', icon: 'VerticalAlignBottomOutlined' }
  ];

  // Grid settings
  const [gridSettings, setGridSettings] = useState({
    columns: 12,
    gap: '16px',
    padding: '16px',
    responsive: true
  });

  // Responsive breakpoints
  const [breakpoints, setBreakpoints] = useState({
    xs: 576,
    sm: 768,
    md: 992,
    lg: 1200,
    xl: 1400
  });

  // Layout tool handlers
  const handleLayoutTool = (toolId) => {
    if (!selectedItem) {
      if (typeof message !== 'undefined') {
        message.warning('Please select an item first');
      }
      return;
    }

    switch (toolId) {
      case 'align-left':
        updateItemPosition(selectedItem.id, { x: 1 });
        break;
      case 'align-center':
        updateItemPosition(selectedItem.id, { x: Math.floor(gridSettings.columns / 2) });
        break;
      case 'align-right':
        updateItemPosition(selectedItem.id, { x: gridSettings.columns - selectedItem.width });
        break;
      case 'bring-forward':
        updateItemZIndex(selectedItem.id, 1);
        break;
      case 'send-backward':
        updateItemZIndex(selectedItem.id, -1);
        break;
      default:
        console.log('Tool not implemented:', toolId);
    }
  };

  // Helper functions for layout tools
  const updateItemPosition = (itemId, position) => {
    const updatedItems = layoutItems.map(item => {
      if (item.id === itemId) {
        return { ...item, ...position };
      }
      return item;
    });
    setLayoutItems(updatedItems);
  };

  const updateItemZIndex = (itemId, change) => {
    const updatedItems = layoutItems.map(item => {
      if (item.id === itemId) {
        return { ...item, zIndex: (item.zIndex || 0) + change };
      }
      return item;
    });
    setLayoutItems(updatedItems);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!layoutName.trim()) {
      newErrors.name = 'Layout name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddLayout = () => {
    if (!validateForm()) return;

    try {
      const newLayout = {
        id: Date.now().toString(),
        name: layoutName.trim(),
        type: layoutType,
        items: layoutItems,
        settings: {
          columns: layoutType === 'grid' ? 12 : 1,
          gap: '16px',
          padding: '16px',
          responsive: true,
          breakpoints: {
            xs: 576,
            sm: 768,
            md: 992,
            lg: 1200,
            xl: 1400
          }
        },
        createdAt: new Date().toISOString()
      };

      dispatch(addLayout(newLayout));

      // Reset form
      setLayoutName('');
      setLayoutType('grid');
      setLayoutItems([]);
      setErrors({});

      // Show success message
      if (typeof message !== 'undefined') {
        message.success(`Layout "${newLayout.name}" created successfully`);
      }

      console.log('Layout added successfully:', newLayout);
    } catch (error) {
      console.error('Error adding layout:', error);
      setErrors({ submit: 'Failed to add layout' });

      // Show error message
      if (typeof message !== 'undefined') {
        message.error('Failed to create layout');
      }
    }
  };

  const handleUpdateLayout = () => {
    if (!selectedLayout || !validateForm()) return;

    try {
      const updatedLayout = {
        ...selectedLayout,
        name: layoutName.trim(),
        type: layoutType,
        items: layoutItems,
        updatedAt: new Date().toISOString()
      };

      dispatch(updateLayout(updatedLayout.id, updatedLayout));

      // Reset form and exit edit mode
      setLayoutName('');
      setLayoutType('grid');
      setLayoutItems([]);
      setSelectedLayout(null);
      setEditMode(false);
      setErrors({});

      console.log('Layout updated successfully:', updatedLayout);
    } catch (error) {
      console.error('Error updating layout:', error);
      setErrors({ submit: 'Failed to update layout' });
    }
  };

  const handleRemoveLayout = (id) => {
    try {
      dispatch(removeLayout(id));

      // If the removed layout was selected, reset the form
      if (selectedLayout && selectedLayout.id === id) {
        setLayoutName('');
        setLayoutType('grid');
        setLayoutItems([]);
        setSelectedLayout(null);
        setEditMode(false);
      }

      console.log('Layout removed successfully:', id);
    } catch (error) {
      console.error('Error removing layout:', error);
    }
  };

  const handleSelectLayout = (layout) => {
    setSelectedLayout(layout);
    setLayoutName(layout.name);
    setLayoutType(layout.type);
    setLayoutItems(layout.items || []);
    setEditMode(true);
    setErrors({});
  };

  const handleCancelEdit = () => {
    setLayoutName('');
    setLayoutType('grid');
    setLayoutItems([]);
    setSelectedLayout(null);
    setEditMode(false);
    setErrors({});
  };

  const handleDuplicateLayout = (layout) => {
    const duplicatedLayout = {
      ...layout,
      id: Date.now().toString(),
      name: `${layout.name} (Copy)`,
      createdAt: new Date().toISOString()
    };

    dispatch(addLayout(duplicatedLayout));
  };

  const handleDrop = (item, position) => {
    // Find the component from the components list
    const component = components.find(c => c.id === item.id);

    if (component) {
      // Calculate grid position based on drop coordinates
      const gridX = position?.gridX || Math.floor(position?.relativeX * 12) || 1;
      const gridY = position?.gridY || Math.floor(position?.relativeY * 12) || 1;

      const dropPosition = {
        x: gridX,
        y: gridY,
        width: itemWidth,
        height: itemHeight
      };

      // If we have a selected layout, add to that layout
      if (selectedLayout) {
        try {
          dispatch(addComponentToLayout(selectedLayout.id, component.id, dropPosition));
          if (typeof message !== 'undefined') {
            message.success(`Component "${component.name}" added to layout "${selectedLayout.name}"`);
          }
        } catch (error) {
          console.warn('Using fallback drop logic');
          const newItem = {
            id: Date.now().toString(),
            componentId: component.id,
            name: component.name,
            type: component.type,
            ...dropPosition
          };
          setLayoutItems([...layoutItems, newItem]);
        }
      } else {
        // Add to current working layout
        const newItem = {
          id: Date.now().toString(),
          componentId: component.id,
          name: component.name,
          type: component.type,
          ...dropPosition
        };
        setLayoutItems([...layoutItems, newItem]);
      }
    }
  };

  const handleSelectItem = (item) => {
    setSelectedItem(item);
    setItemWidth(item.width);
    setItemHeight(item.height);
  };

  const handleUpdateItem = () => {
    if (!selectedItem) return;

    const updatedItems = layoutItems.map(item => {
      if (item.id === selectedItem.id) {
        return {
          ...item,
          width: itemWidth,
          height: itemHeight
        };
      }
      return item;
    });

    setLayoutItems(updatedItems);
    setSelectedItem(null);
  };

  const handleRemoveItem = (itemId) => {
    setLayoutItems(layoutItems.filter(item => item.id !== itemId));

    if (selectedItem && selectedItem.id === itemId) {
      setSelectedItem(null);
    }
  };

  return (
    <LayoutDesignerContainer>
      <Card>
        <Card.Header>
          <Card.Title>{editMode ? 'Edit Layout' : 'Create Layout'}</Card.Title>
          {editMode && (
            <Button
              variant="text"
              size="small"
              onClick={handleCancelEdit}
              startIcon={<CloseOutlined />}
            >
              Cancel
            </Button>
          )}
        </Card.Header>
        <Card.Content>
          <PropertyEditor>
            <PropertyGroup>
              <Input
                label="Layout Name"
                value={layoutName}
                onChange={(e) => setLayoutName(e.target.value)}
                placeholder="Enter layout name"
                fullWidth
                error={!!errors.name}
                helperText={errors.name}
              />
            </PropertyGroup>

            <PropertyGroup>
              <Select
                label="Layout Type"
                value={layoutType}
                onChange={(e) => setLayoutType(e.target.value)}
                options={layoutTypes}
                fullWidth
              />
            </PropertyGroup>
          </PropertyEditor>
        </Card.Content>
        <Card.Footer>
          {editMode ? (
            <Button
              variant="primary"
              onClick={handleUpdateLayout}
              startIcon={<SaveOutlined />}
            >
              Update Layout
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleAddLayout}
              startIcon={<PlusOutlined />}
            >
              Add Layout
            </Button>
          )}
        </Card.Footer>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Layout Designer</Card.Title>
        </Card.Header>
        <Card.Content>
          {/* Layout Tools Panel */}
          <div style={{
            marginBottom: theme.spacing[4],
            padding: theme.spacing[3],
            backgroundColor: theme.colors.neutral[50],
            borderRadius: theme.borderRadius.md,
            border: `1px solid ${theme.colors.neutral[200]}`
          }}>
            <div style={{
              marginBottom: theme.spacing[2],
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.neutral[700]
            }}>
              Layout Tools
            </div>
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: theme.spacing[1]
            }}>
              {layoutTools.map(tool => (
                <Button
                  key={tool.id}
                  variant="text"
                  size="small"
                  onClick={() => handleLayoutTool(tool.id)}
                  disabled={!selectedItem && ['align-left', 'align-center', 'align-right', 'bring-forward', 'send-backward'].includes(tool.id)}
                  style={{
                    minWidth: 'auto',
                    padding: `${theme.spacing[1]} ${theme.spacing[2]}`,
                    fontSize: theme.typography.fontSize.xs
                  }}
                  title={tool.label}
                >
                  {tool.label}
                </Button>
              ))}
            </div>
            {selectedItem && (
              <div style={{
                marginTop: theme.spacing[2],
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.neutral[600]
              }}>
                Selected: {selectedItem.name} ({selectedItem.width}×{selectedItem.height})
              </div>
            )}
          </div>

          <ComponentPalette>
            <div style={{ marginRight: theme.spacing[4], fontWeight: theme.typography.fontWeight.medium }}>
              Component Palette:
            </div>
            {components.map(component => (
              <PaletteItem
                key={component.id}
                draggable
                onDragStart={(e) => {
                  // Set the drag data
                  e.dataTransfer.setData('application/json', JSON.stringify({
                    id: component.id,
                    name: component.name,
                    type: component.type
                  }));
                  e.dataTransfer.effectAllowed = 'copy';
                }}
              >
                <DragOutlined />
                <span>{component.name}</span>
              </PaletteItem>
            ))}
            {components.length === 0 && (
              <div style={{ color: theme.colors.neutral[500] }}>
                No components available. Create components first.
              </div>
            )}
          </ComponentPalette>

          <GridControls>
            <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[2] }}>
              <ColumnWidthOutlined />
              <Input
                label="Width"
                type="number"
                min={1}
                max={12}
                value={itemWidth}
                onChange={(e) => setItemWidth(parseInt(e.target.value, 10))}
                style={{ width: '80px' }}
              />
            </div>

            <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[2] }}>
              <ColumnHeightOutlined />
              <Input
                label="Height"
                type="number"
                min={1}
                max={12}
                value={itemHeight}
                onChange={(e) => setItemHeight(parseInt(e.target.value, 10))}
                style={{ width: '80px' }}
              />
            </div>

            {selectedItem && (
              <Button
                variant="primary"
                size="small"
                onClick={handleUpdateItem}
              >
                Apply
              </Button>
            )}
          </GridControls>

          <DropZone
            targetId={selectedLayout?.id || 'current-layout'}
            targetType="layout"
            onDrop={handleDrop}
            acceptedTypes={['component']}
            showDropIndicator={true}
            style={{ minHeight: '400px' }}
          >
            <LayoutCanvas>
              {layoutItems.length === 0 && (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '300px',
                  color: theme.colors.neutral[500],
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '48px', marginBottom: theme.spacing[4] }}>
                    📐
                  </div>
                  <h3>Drop Components Here</h3>
                  <p>Drag components from the Component Builder to create your layout</p>
                </div>
              )}

              {layoutItems.map((item, index) => (
                <DraggableComponent
                  key={item.id}
                  component={item}
                  index={index}
                  onSelect={() => handleSelectItem(item)}
                  isSelected={selectedItem && selectedItem.id === item.id}
                  onRemove={handleRemoveItem}
                  onDragEnd={(component, position) => {
                    // Update the component position in the layout
                    const updatedItems = layoutItems.map(layoutItem => {
                      if (layoutItem.id === component.id) {
                        return {
                          ...layoutItem,
                          x: position.x,
                          y: position.y
                        };
                      }
                      return layoutItem;
                    });
                    setLayoutItems(updatedItems);
                  }}
                />
              ))}
            </LayoutCanvas>
          </DropZone>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Saved Layouts</Card.Title>
        </Card.Header>
        <Card.Content>
          {layouts.length === 0 ? (
            <EmptyState>
              <div style={{ fontSize: '48px', color: theme.colors.neutral[400], marginBottom: theme.spacing[4] }}>
                <BorderOutlined />
              </div>
              <h3>No Layouts Yet</h3>
              <p>Create your first layout to get started</p>
            </EmptyState>
          ) : (
            <LayoutGrid>
              {layouts.map(layout => (
                <Card key={layout.id} elevation="sm">
                  <Card.Header>
                    <div>
                      <div style={{ fontWeight: theme.typography.fontWeight.semibold }}>{layout.name}</div>
                      <div style={{ fontSize: theme.typography.fontSize.sm, color: theme.colors.neutral[500] }}>
                        {layout.type}
                      </div>
                    </div>
                    <div style={{ display: 'flex', gap: theme.spacing[1] }}>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => handleDuplicateLayout(layout)}
                      >
                        <CopyOutlined />
                      </Button>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => handleSelectLayout(layout)}
                      >
                        <EditOutlined />
                      </Button>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => handleRemoveLayout(layout.id)}
                      >
                        <DeleteOutlined />
                      </Button>
                    </div>
                  </Card.Header>
                  <Card.Content onClick={() => handleSelectLayout(layout)}>
                    <div style={{
                      height: '150px',
                      backgroundColor: theme.colors.neutral[100],
                      borderRadius: theme.borderRadius.md,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer'
                    }}>
                      <EyeOutlined style={{ fontSize: '24px', color: theme.colors.neutral[400] }} />
                    </div>
                  </Card.Content>
                  <Card.Footer>
                    <div style={{ fontSize: theme.typography.fontSize.sm, color: theme.colors.neutral[500] }}>
                      {layout.items?.length || 0} components
                    </div>
                  </Card.Footer>
                </Card>
              ))}
            </LayoutGrid>
          )}
        </Card.Content>
      </Card>

      {/* Drag preview for visual feedback */}
      <DragPreview />
    </LayoutDesignerContainer>
  );
};

export default LayoutDesigner;
