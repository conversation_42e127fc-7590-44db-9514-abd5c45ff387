"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9783],{

/***/ 9783:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(677);
/* harmony import */ var antd_es_typography__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5475);
/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1850);
/* harmony import */ var antd_es_space__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8392);
/* harmony import */ var antd_es_row__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7152);
/* harmony import */ var antd_es_col__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6370);
/* harmony import */ var antd_es_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(2395);
/* harmony import */ var antd_es_input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(7355);
/* harmony import */ var antd_es_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(4358);
/* harmony import */ var _ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(8244);
/* harmony import */ var _ant_design_icons_es_icons_LayoutOutlined__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(7290);
/* harmony import */ var _ant_design_icons_es_icons_BgColorsOutlined__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(271);
/* harmony import */ var _ant_design_icons_es_icons_CodeOutlined__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(5937);
/* harmony import */ var _ant_design_icons_es_icons_PlusOutlined__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(8940);
/* harmony import */ var _ant_design_icons_es_icons_DeleteOutlined__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(9499);
/* harmony import */ var _ant_design_icons_es_icons_DragOutlined__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(1022);
/* harmony import */ var _ant_design_icons_es_icons_EyeOutlined__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(1387);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


















var Title = antd_es_typography__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.Title,
  Text = antd_es_typography__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.Text;
var TextArea = antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A.TextArea;

/**
 * Lightweight App Builder - Optimized for Bundle Size
 * Core app building functionality without heavy dependencies
 */
var LightweightAppBuilder = function LightweightAppBuilder() {
  // Component Builder State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    components = _useState2[0],
    setComponents = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    componentName = _useState4[0],
    setComponentName = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('button'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    componentType = _useState6[0],
    setComponentType = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('{}'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    componentProps = _useState8[0],
    setComponentProps = _useState8[1];

  // Layout Designer State
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    layouts = _useState0[0],
    setLayouts = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    layoutName = _useState10[0],
    setLayoutName = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    selectedLayout = _useState12[0],
    setSelectedLayout = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState13, 2),
    layoutComponents = _useState14[0],
    setLayoutComponents = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState15, 2),
    draggedComponent = _useState16[0],
    setDraggedComponent = _useState16[1];
  var dropZoneRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);

  // Theme Manager State
  var _useState17 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([{
      id: 'default',
      name: 'Default',
      primaryColor: '#1890ff',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      secondaryColor: '#f0f0f0',
      borderRadius: '6px',
      fontSize: '14px'
    }, {
      id: 'dark',
      name: 'Dark Mode',
      primaryColor: '#1890ff',
      backgroundColor: '#141414',
      textColor: '#ffffff',
      secondaryColor: '#262626',
      borderRadius: '6px',
      fontSize: '14px'
    }, {
      id: 'modern',
      name: 'Modern',
      primaryColor: '#722ed1',
      backgroundColor: '#f5f5f5',
      textColor: '#262626',
      secondaryColor: '#e6f7ff',
      borderRadius: '12px',
      fontSize: '16px'
    }]),
    _useState18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState17, 2),
    themes = _useState18[0],
    setThemes = _useState18[1];
  var _useState19 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
      name: 'Custom',
      primaryColor: '#1890ff',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      secondaryColor: '#f0f0f0',
      borderRadius: '6px',
      fontSize: '14px'
    }),
    _useState20 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState19, 2),
    currentTheme = _useState20[0],
    setCurrentTheme = _useState20[1];

  // Code Export State
  var _useState21 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState22 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState21, 2),
    generatedCode = _useState22[0],
    setGeneratedCode = _useState22[1];

  // Component Types
  var componentTypes = [{
    value: 'button',
    label: 'Button'
  }, {
    value: 'input',
    label: 'Input'
  }, {
    value: 'text',
    label: 'Text'
  }, {
    value: 'card',
    label: 'Card'
  }, {
    value: 'container',
    label: 'Container'
  }];

  // Component Builder Functions
  var addComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (!componentName.trim()) return;
    try {
      var props = JSON.parse(componentProps);
      var newComponent = {
        id: Date.now().toString(),
        name: componentName.trim(),
        type: componentType,
        props: props,
        createdAt: new Date().toISOString()
      };
      setComponents(function (prev) {
        return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newComponent]);
      });
      setComponentName('');
      setComponentProps('{}');
    } catch (error) {
      alert('Invalid JSON in props: ' + error.message);
    }
  }, [componentName, componentType, componentProps]);
  var removeComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (id) {
    setComponents(function (prev) {
      return prev.filter(function (comp) {
        return comp.id !== id;
      });
    });
  }, []);

  // Drag and Drop Functions
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e, component) {
    setDraggedComponent(component);
    e.dataTransfer.effectAllowed = 'copy';
  }, []);
  var handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  }, []);
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    if (draggedComponent) {
      var newLayoutComponent = _objectSpread(_objectSpread({}, draggedComponent), {}, {
        id: Date.now().toString(),
        x: Math.random() * 300,
        y: Math.random() * 200,
        layoutId: Date.now().toString()
      });
      setLayoutComponents(function (prev) {
        return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newLayoutComponent]);
      });
      setDraggedComponent(null);
    }
  }, [draggedComponent]);
  var removeLayoutComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (id) {
    setLayoutComponents(function (prev) {
      return prev.filter(function (comp) {
        return comp.id !== id;
      });
    });
  }, []);

  // Layout Designer Functions
  var addLayout = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (!layoutName.trim()) return;
    var newLayout = {
      id: Date.now().toString(),
      name: layoutName.trim(),
      components: (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(layoutComponents),
      createdAt: new Date().toISOString()
    };
    setLayouts(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newLayout]);
    });
    setLayoutName('');
    setLayoutComponents([]);
  }, [layoutName, layoutComponents]);

  // Theme Manager Functions
  var saveTheme = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    var newTheme = _objectSpread(_objectSpread({
      id: Date.now().toString()
    }, currentTheme), {}, {
      createdAt: new Date().toISOString()
    });
    setThemes(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newTheme]);
    });
  }, [currentTheme]);
  var applyTheme = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (theme) {
    setCurrentTheme(_objectSpread({}, theme));
  }, []);
  var resetTheme = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setCurrentTheme({
      name: 'Custom',
      primaryColor: '#1890ff',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      secondaryColor: '#f0f0f0',
      borderRadius: '6px',
      fontSize: '14px'
    });
  }, []);

  // Code Export Functions
  var generateCode = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    var code = "\n// Generated App Builder Code\nimport React from 'react';\n\nconst theme = {\n  primaryColor: '".concat(currentTheme.primaryColor, "',\n  backgroundColor: '").concat(currentTheme.backgroundColor, "',\n  textColor: '").concat(currentTheme.textColor, "',\n  secondaryColor: '").concat(currentTheme.secondaryColor, "',\n  borderRadius: '").concat(currentTheme.borderRadius, "',\n  fontSize: '").concat(currentTheme.fontSize, "'\n};\n\nconst GeneratedApp = () => {\n  return (\n    <div style={{\n      backgroundColor: theme.backgroundColor,\n      color: theme.textColor,\n      minHeight: '100vh',\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif',\n      fontSize: theme.fontSize\n    }}>\n      <h1 style={{\n        color: theme.primaryColor,\n        marginBottom: '24px',\n        fontSize: 'calc(' + theme.fontSize + ' * 2)'\n      }}>\n        Generated Application\n      </h1>\n\n      {/* Layout Components */}\n      <div style={{ position: 'relative', minHeight: '400px' }}>\n        ").concat(layoutComponents.map(function (comp) {
      return "\n        <div key=\"".concat(comp.id, "\" style={{\n          position: 'absolute',\n          left: '").concat(comp.x, "px',\n          top: '").concat(comp.y, "px'\n        }}>\n          ").concat(generateComponentCode(comp), "\n        </div>");
    }).join(''), "\n      </div>\n\n      {/* Regular Components */}\n      <div style={{ marginTop: '40px' }}>\n        ").concat(components.filter(function (comp) {
      return !layoutComponents.find(function (lc) {
        return lc.name === comp.name;
      });
    }).map(function (comp) {
      return "\n        <div key=\"".concat(comp.id, "\" style={{ margin: '10px 0' }}>\n          ").concat(generateComponentCode(comp), "\n        </div>");
    }).join(''), "\n      </div>\n    </div>\n  );\n};\n\nexport default GeneratedApp;\n");
    setGeneratedCode(code);
  }, [components, layoutComponents, currentTheme]);
  var generateComponentCode = function generateComponentCode(component) {
    switch (component.type) {
      case 'button':
        return "<button style={{\n          backgroundColor: theme.primaryColor,\n          color: 'white',\n          padding: '12px 24px',\n          border: 'none',\n          borderRadius: theme.borderRadius,\n          fontSize: theme.fontSize,\n          cursor: 'pointer',\n          fontWeight: '500'\n        }}>".concat(component.name, "</button>");
      case 'input':
        return "<input placeholder=\"".concat(component.name, "\" style={{\n          padding: '12px',\n          border: '1px solid #d9d9d9',\n          borderRadius: theme.borderRadius,\n          fontSize: theme.fontSize,\n          width: '200px'\n        }} />");
      case 'text':
        return "<p style={{\n          color: theme.textColor,\n          fontSize: theme.fontSize,\n          margin: '8px 0'\n        }}>".concat(component.name, "</p>");
      case 'card':
        return "<div style={{\n          border: '1px solid #d9d9d9',\n          borderRadius: theme.borderRadius,\n          padding: '16px',\n          backgroundColor: theme.secondaryColor,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n        }}>".concat(component.name, "</div>");
      case 'container':
        return "<div style={{\n          padding: '20px',\n          border: '2px dashed #d9d9d9',\n          borderRadius: theme.borderRadius,\n          backgroundColor: theme.secondaryColor,\n          minHeight: '100px'\n        }}>".concat(component.name, "</div>");
      default:
        return "<div style={{\n          padding: '8px',\n          color: theme.textColor,\n          fontSize: theme.fontSize\n        }}>".concat(component.name, "</div>");
    }
  };

  // Tab Items
  var tabItems = [{
    key: 'components',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A, null), "Components"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Component Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      placeholder: "Component Name",
      value: componentName,
      onChange: function onChange(e) {
        return setComponentName(e.target.value);
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_select__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .A, {
      value: componentType,
      onChange: setComponentType,
      style: {
        width: '100%'
      },
      options: componentTypes
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, {
      placeholder: "Component Props (JSON)",
      value: componentProps,
      onChange: function onChange(e) {
        return setComponentProps(e.target.value);
      },
      rows: 3
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_PlusOutlined__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .A, null),
      onClick: addComponent
    }, "Add Component")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4,
      style: {
        marginTop: '24px'
      }
    }, "Components (", components.length, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        maxHeight: '300px',
        overflowY: 'auto'
      }
    }, components.map(function (comp) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: comp.id,
        size: "small",
        style: {
          marginBottom: '8px',
          cursor: 'grab',
          border: '1px solid #d9d9d9'
        },
        draggable: true,
        onDragStart: function onDragStart(e) {
          return handleDragStart(e, comp);
        },
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_DragOutlined__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .A, {
          style: {
            color: '#1890ff'
          }
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
          type: "text",
          danger: true,
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_DeleteOutlined__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .A, null),
          onClick: function onClick() {
            return removeComponent(comp.id);
          }
        }))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, comp.name), " (", comp.type, ")");
    })))
  }, {
    key: 'layout',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_LayoutOutlined__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .A, null), "Layout"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Visual Layout Designer"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_row__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
      gutter: 16
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Component Palette",
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      type: "secondary"
    }, "Drag components from the Components tab to the canvas")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Layout Canvas",
      size: "small",
      style: {
        marginTop: '16px',
        minHeight: '400px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      ref: dropZoneRef,
      onDragOver: handleDragOver,
      onDrop: handleDrop,
      style: {
        minHeight: '350px',
        border: '2px dashed #d9d9d9',
        borderRadius: '8px',
        position: 'relative',
        backgroundColor: '#fafafa',
        padding: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      type: "secondary",
      style: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
      }
    }, "Drop components here to design your layout"), layoutComponents.map(function (comp) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        key: comp.id,
        style: {
          position: 'absolute',
          left: comp.x,
          top: comp.y,
          padding: '8px',
          backgroundColor: 'white',
          border: '1px solid #1890ff',
          borderRadius: '4px',
          cursor: 'move',
          minWidth: '100px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, comp.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
        type: "text",
        size: "small",
        danger: true,
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_DeleteOutlined__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .A, null),
        onClick: function onClick() {
          return removeLayoutComponent(comp.id);
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        type: "secondary",
        style: {
          fontSize: '12px'
        }
      }, comp.type));
    })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Layout Controls",
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      placeholder: "Layout Name",
      value: layoutName,
      onChange: function onChange(e) {
        return setLayoutName(e.target.value);
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      onClick: addLayout,
      disabled: !layoutName.trim()
    }, "Save Layout"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      onClick: function onClick() {
        return setLayoutComponents([]);
      },
      disabled: layoutComponents.length === 0
    }, "Clear Canvas")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 5,
      style: {
        marginTop: '16px'
      }
    }, "Canvas Components (", layoutComponents.length, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        maxHeight: '200px',
        overflowY: 'auto'
      }
    }, layoutComponents.map(function (comp) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: comp.id,
        size: "small",
        style: {
          marginBottom: '4px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, comp.name, " (", comp.type, ")"));
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Saved Layouts",
      size: "small",
      style: {
        marginTop: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 5
    }, "Layouts (", layouts.length, ")"), layouts.map(function (layout) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: layout.id,
        size: "small",
        style: {
          marginBottom: '8px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, layout.name), " - ", layout.components.length, " components", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
        size: "small",
        type: "link",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_EyeOutlined__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .A, null),
        onClick: function onClick() {
          return setLayoutComponents((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(layout.components));
        }
      }, "Load Layout"));
    })))))
  }, {
    key: 'theme',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_BgColorsOutlined__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .A, null), "Theme"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Advanced Theme Manager"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_row__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
      gutter: 16
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Theme Presets",
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, themes.slice(0, 3).map(function (theme) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: theme.id,
        size: "small",
        style: {
          cursor: 'pointer',
          border: currentTheme.name === theme.name ? '2px solid #1890ff' : '1px solid #d9d9d9'
        },
        onClick: function onClick() {
          return applyTheme(theme);
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, theme.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          gap: '4px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '16px',
          height: '16px',
          backgroundColor: theme.primaryColor,
          borderRadius: '2px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '16px',
          height: '16px',
          backgroundColor: theme.backgroundColor,
          border: '1px solid #ccc',
          borderRadius: '2px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '16px',
          height: '16px',
          backgroundColor: theme.textColor,
          borderRadius: '2px'
        }
      }))));
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Theme Preview",
      size: "small",
      style: {
        marginTop: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        backgroundColor: currentTheme.backgroundColor,
        color: currentTheme.textColor,
        padding: '16px',
        borderRadius: currentTheme.borderRadius,
        border: '1px solid #d9d9d9'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("h4", {
      style: {
        color: currentTheme.primaryColor,
        margin: '0 0 8px 0',
        fontSize: currentTheme.fontSize
      }
    }, "Preview Title"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("p", {
      style: {
        margin: '0 0 12px 0',
        fontSize: currentTheme.fontSize
      }
    }, "This is how your theme will look in the generated application."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("button", {
      style: {
        backgroundColor: currentTheme.primaryColor,
        color: 'white',
        border: 'none',
        padding: '8px 16px',
        borderRadius: currentTheme.borderRadius,
        fontSize: currentTheme.fontSize,
        cursor: 'pointer'
      }
    }, "Sample Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        backgroundColor: currentTheme.secondaryColor,
        padding: '12px',
        marginTop: '12px',
        borderRadius: currentTheme.borderRadius
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      style: {
        fontSize: currentTheme.fontSize
      }
    }, "Secondary background area"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Custom Theme Editor",
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Theme Name:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      value: currentTheme.name,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            name: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_row__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
      gutter: 8
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Primary Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      type: "color",
      value: currentTheme.primaryColor,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            primaryColor: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px',
        width: '100%'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Background Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      type: "color",
      value: currentTheme.backgroundColor,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            backgroundColor: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px',
        width: '100%'
      }
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_row__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
      gutter: 8
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Text Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      type: "color",
      value: currentTheme.textColor,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            textColor: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px',
        width: '100%'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Secondary Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_input__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {
      type: "color",
      value: currentTheme.secondaryColor,
      onChange: function onChange(e) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            secondaryColor: e.target.value
          });
        });
      },
      style: {
        marginTop: '4px',
        width: '100%'
      }
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_row__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
      gutter: 8
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Border Radius:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_select__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .A, {
      value: currentTheme.borderRadius,
      onChange: function onChange(value) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            borderRadius: value
          });
        });
      },
      style: {
        marginTop: '4px',
        width: '100%'
      },
      options: [{
        value: '0px',
        label: 'None (0px)'
      }, {
        value: '4px',
        label: 'Small (4px)'
      }, {
        value: '6px',
        label: 'Medium (6px)'
      }, {
        value: '8px',
        label: 'Large (8px)'
      }, {
        value: '12px',
        label: 'Extra Large (12px)'
      }]
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
      span: 12
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Font Size:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_select__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .A, {
      value: currentTheme.fontSize,
      onChange: function onChange(value) {
        return setCurrentTheme(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            fontSize: value
          });
        });
      },
      style: {
        marginTop: '4px',
        width: '100%'
      },
      options: [{
        value: '12px',
        label: 'Small (12px)'
      }, {
        value: '14px',
        label: 'Medium (14px)'
      }, {
        value: '16px',
        label: 'Large (16px)'
      }, {
        value: '18px',
        label: 'Extra Large (18px)'
      }]
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      onClick: saveTheme
    }, "Save Custom Theme"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      onClick: resetTheme
    }, "Reset")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
      title: "Saved Custom Themes",
      size: "small",
      style: {
        marginTop: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 5
    }, "Custom Themes (", themes.length - 3, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        maxHeight: '200px',
        overflowY: 'auto'
      }
    }, themes.slice(3).map(function (theme) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
        key: theme.id,
        size: "small",
        style: {
          marginBottom: '8px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, theme.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          gap: '4px',
          marginTop: '4px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '16px',
          height: '16px',
          backgroundColor: theme.primaryColor,
          borderRadius: '2px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '16px',
          height: '16px',
          backgroundColor: theme.backgroundColor,
          border: '1px solid #ccc',
          borderRadius: '2px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '16px',
          height: '16px',
          backgroundColor: theme.textColor,
          borderRadius: '2px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          width: '16px',
          height: '16px',
          backgroundColor: theme.secondaryColor,
          borderRadius: '2px'
        }
      }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
        size: "small",
        onClick: function onClick() {
          return applyTheme(theme);
        }
      }, "Apply")));
    }))))))
  }, {
    key: 'export',
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_CodeOutlined__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .A, null), "Export"),
    children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3
    }, "Code Export"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, {
      type: "primary",
      onClick: generateCode
    }, "Generate Code"), generatedCode && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, {
      value: generatedCode,
      rows: 20,
      readOnly: true,
      style: {
        fontFamily: 'monospace',
        fontSize: '12px'
      }
    })))
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 1
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A, {
    style: {
      marginRight: '16px',
      color: '#1890ff'
    }
  }), "Lightweight App Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '16px',
      display: 'block',
      marginBottom: '24px'
    }
  }, "Build applications visually with our optimized, lightweight interface"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_tabs__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, {
    items: tabItems,
    size: "large"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LightweightAppBuilder);

/***/ })

}]);