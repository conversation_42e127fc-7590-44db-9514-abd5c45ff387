"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[520],{

/***/ 60960:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _ant_design_icons_svg_es_asn_PhoneFilled__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5249);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12226);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var PhoneFilled = function PhoneFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PhoneFilledSvg
  }));
};

/**![phone](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4NS42IDIzMC4yTDc3OS4xIDEyMy44YTgwLjgzIDgwLjgzIDAgMDAtNTcuMy0yMy44Yy0yMS43IDAtNDIuMSA4LjUtNTcuNCAyMy44TDU0OS44IDIzOC40YTgwLjgzIDgwLjgzIDAgMDAtMjMuOCA1Ny4zYzAgMjEuNyA4LjUgNDIuMSAyMy44IDU3LjRsODMuOCA4My44QTM5My44MiAzOTMuODIgMCAwMTU1My4xIDU1MyAzOTUuMzQgMzk1LjM0IDAgMDE0MzcgNjMzLjhMMzUzLjIgNTUwYTgwLjgzIDgwLjgzIDAgMDAtNTcuMy0yMy44Yy0yMS43IDAtNDIuMSA4LjUtNTcuNCAyMy44TDEyMy44IDY2NC41YTgwLjg5IDgwLjg5IDAgMDAtMjMuOCA1Ny40YzAgMjEuNyA4LjUgNDIuMSAyMy44IDU3LjRsMTA2LjMgMTA2LjNjMjQuNCAyNC41IDU4LjEgMzguNCA5Mi43IDM4LjQgNy4zIDAgMTQuMy0uNiAyMS4yLTEuOCAxMzQuOC0yMi4yIDI2OC41LTkzLjkgMzc2LjQtMjAxLjdDODI4LjIgNjEyLjggODk5LjggNDc5LjIgOTIyLjMgMzQ0YzYuOC00MS4zLTYuOS04My44LTM2LjctMTEzLjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/(/* unused pure expression or super */ null && (React.forwardRef(PhoneFilled)));
if (false) {}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (RefIcon)));

/***/ }),

/***/ 73242:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _ant_design_icons_svg_es_asn_PhoneOutlined__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(77775);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12226);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var PhoneOutlined = function PhoneOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PhoneOutlinedSvg
  }));
};

/**![phone](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3Ny4xIDIzOC43TDc3MC42IDEzMi4zYy0xMy0xMy0zMC40LTIwLjMtNDguOC0yMC4zcy0zNS44IDcuMi00OC44IDIwLjNMNTU4LjMgMjQ2LjhjLTEzIDEzLTIwLjMgMzAuNS0yMC4zIDQ4LjkgMCAxOC41IDcuMiAzNS44IDIwLjMgNDguOWw4OS42IDg5LjdhNDA1LjQ2IDQwNS40NiAwIDAxLTg2LjQgMTI3LjNjLTM2LjcgMzYuOS03OS42IDY2LTEyNy4yIDg2LjZsLTg5LjYtODkuN2MtMTMtMTMtMzAuNC0yMC4zLTQ4LjgtMjAuM2E2OC4yIDY4LjIgMCAwMC00OC44IDIwLjNMMTMyLjMgNjczYy0xMyAxMy0yMC4zIDMwLjUtMjAuMyA0OC45IDAgMTguNSA3LjIgMzUuOCAyMC4zIDQ4LjlsMTA2LjQgMTA2LjRjMjIuMiAyMi4yIDUyLjggMzQuOSA4NC4yIDM0LjkgNi41IDAgMTIuOC0uNSAxOS4yLTEuNiAxMzIuNC0yMS44IDI2My44LTkyLjMgMzY5LjktMTk4LjNDODE4IDYwNiA4ODguNCA0NzQuNiA5MTAuNCAzNDIuMWM2LjMtMzcuNi02LjMtNzYuMy0zMy4zLTEwMy40em0tMzcuNiA5MS41Yy0xOS41IDExNy45LTgyLjkgMjM1LjUtMTc4LjQgMzMxcy0yMTMgMTU4LjktMzMwLjkgMTc4LjRjLTE0LjggMi41LTMwLTIuNS00MC44LTEzLjJMMTg0LjkgNzIxLjkgMjk1LjcgNjExbDExOS44IDEyMCAuOS45IDIxLjYtOGE0ODEuMjkgNDgxLjI5IDAgMDAyODUuNy0yODUuOGw4LTIxLjYtMTIwLjgtMTIwLjcgMTEwLjgtMTEwLjkgMTA0LjUgMTA0LjVjMTAuOCAxMC44IDE1LjggMjYgMTMuMyA0MC44eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/(/* unused pure expression or super */ null && (React.forwardRef(PhoneOutlined)));
if (false) {}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (RefIcon)));

/***/ }),

/***/ 93300:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _ant_design_icons_svg_es_asn_PhoneTwoTone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(82287);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12226);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var PhoneTwoTone = function PhoneTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PhoneTwoToneSvg
  }));
};

/**![phone](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcyMS43IDE4NC45TDYxMC45IDI5NS44bDEyMC44IDEyMC43LTggMjEuNkE0ODEuMjkgNDgxLjI5IDAgMDE0MzggNzIzLjlsLTIxLjYgOC0uOS0uOS0xMTkuOC0xMjAtMTEwLjggMTEwLjkgMTA0LjUgMTA0LjVjMTAuOCAxMC43IDI2IDE1LjcgNDAuOCAxMy4yIDExNy45LTE5LjUgMjM1LjQtODIuOSAzMzAuOS0xNzguNHMxNTguOS0yMTMuMSAxNzguNC0zMzFjMi41LTE0LjgtMi41LTMwLTEzLjMtNDAuOEw3MjEuNyAxODQuOXoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTg3Ny4xIDIzOC43TDc3MC42IDEzMi4zYy0xMy0xMy0zMC40LTIwLjMtNDguOC0yMC4zcy0zNS44IDcuMi00OC44IDIwLjNMNTU4LjMgMjQ2LjhjLTEzIDEzLTIwLjMgMzAuNS0yMC4zIDQ4LjkgMCAxOC41IDcuMiAzNS44IDIwLjMgNDguOWw4OS42IDg5LjdhNDA1LjQ2IDQwNS40NiAwIDAxLTg2LjQgMTI3LjNjLTM2LjcgMzYuOS03OS42IDY2LTEyNy4yIDg2LjZsLTg5LjYtODkuN2MtMTMtMTMtMzAuNC0yMC4zLTQ4LjgtMjAuM2E2OC4yIDY4LjIgMCAwMC00OC44IDIwLjNMMTMyLjMgNjczYy0xMyAxMy0yMC4zIDMwLjUtMjAuMyA0OC45IDAgMTguNSA3LjIgMzUuOCAyMC4zIDQ4LjlsMTA2LjQgMTA2LjRjMjIuMiAyMi4yIDUyLjggMzQuOSA4NC4yIDM0LjkgNi41IDAgMTIuOC0uNSAxOS4yLTEuNiAxMzIuNC0yMS44IDI2My44LTkyLjMgMzY5LjktMTk4LjNDODE4IDYwNiA4ODguNCA0NzQuNiA5MTAuNCAzNDIuMWM2LjMtMzcuNi02LjMtNzYuMy0zMy4zLTEwMy40em0tMzcuNiA5MS41Yy0xOS41IDExNy45LTgyLjkgMjM1LjUtMTc4LjQgMzMxcy0yMTMgMTU4LjktMzMwLjkgMTc4LjRjLTE0LjggMi41LTMwLTIuNS00MC44LTEzLjJMMTg0LjkgNzIxLjkgMjk1LjcgNjExbDExOS44IDEyMCAuOS45IDIxLjYtOGE0ODEuMjkgNDgxLjI5IDAgMDAyODUuNy0yODUuOGw4LTIxLjYtMTIwLjgtMTIwLjcgMTEwLjgtMTEwLjkgMTA0LjUgMTA0LjVjMTAuOCAxMC44IDE1LjggMjYgMTMuMyA0MC44eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/(/* unused pure expression or super */ null && (React.forwardRef(PhoneTwoTone)));
if (false) {}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (RefIcon)));

/***/ })

}]);