"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8792],{

/***/ 6251:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {


// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(6540);
// EXTERNAL MODULE: ./node_modules/react-dom/client.js
var client = __webpack_require__(5338);
// EXTERNAL MODULE: ./node_modules/react-router-dom/dist/index.js
var dist = __webpack_require__(4976);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 5 modules
var spin = __webpack_require__(9029);
;// ./src/MinimalRoutes.js




// Enhanced loading component with progress indication
var SimpleLoading = function SimpleLoading() {
  return /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '300px',
      gap: '16px'
    }
  }, /*#__PURE__*/react.createElement(spin/* default */.A, {
    size: "large"
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      textAlign: 'center',
      color: '#666'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: '16px',
      fontWeight: '500',
      marginBottom: '4px'
    }
  }, "Loading App Builder..."), /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: '14px'
    }
  }, "Optimized for performance \u2022 Bundle size: 2.34MB")));
};

// Lazy load only essential components that don't use styled-components
var HomePage = /*#__PURE__*/react.lazy(function () {
  return Promise.all(/* import() */[__webpack_require__.e(383), __webpack_require__.e(4898), __webpack_require__.e(974), __webpack_require__.e(2812), __webpack_require__.e(7552), __webpack_require__.e(6883), __webpack_require__.e(1031), __webpack_require__.e(7550), __webpack_require__.e(1397), __webpack_require__.e(6051), __webpack_require__.e(5306), __webpack_require__.e(6787), __webpack_require__.e(8994), __webpack_require__.e(9617), __webpack_require__.e(2764), __webpack_require__.e(2558), __webpack_require__.e(1420), __webpack_require__.e(96), __webpack_require__.e(8671), __webpack_require__.e(9956), __webpack_require__.e(2891), __webpack_require__.e(348), __webpack_require__.e(4197), __webpack_require__.e(2538), __webpack_require__.e(8174)]).then(__webpack_require__.bind(__webpack_require__, 8174));
});
var SimpleWebSocketPage = /*#__PURE__*/react.lazy(function () {
  return Promise.all(/* import() */[__webpack_require__.e(383), __webpack_require__.e(4898), __webpack_require__.e(974), __webpack_require__.e(2812), __webpack_require__.e(7552), __webpack_require__.e(6883), __webpack_require__.e(1031), __webpack_require__.e(7550), __webpack_require__.e(1397), __webpack_require__.e(6051), __webpack_require__.e(5306), __webpack_require__.e(6787), __webpack_require__.e(8994), __webpack_require__.e(9617), __webpack_require__.e(2764), __webpack_require__.e(2558), __webpack_require__.e(1420), __webpack_require__.e(96), __webpack_require__.e(8671), __webpack_require__.e(9956), __webpack_require__.e(2891), __webpack_require__.e(348), __webpack_require__.e(4197), __webpack_require__.e(2538), __webpack_require__.e(2211)]).then(__webpack_require__.bind(__webpack_require__, 2211));
});
var LightweightAppBuilder = /*#__PURE__*/react.lazy(function () {
  return Promise.all(/* import() */[__webpack_require__.e(383), __webpack_require__.e(4898), __webpack_require__.e(974), __webpack_require__.e(2812), __webpack_require__.e(7552), __webpack_require__.e(6883), __webpack_require__.e(1031), __webpack_require__.e(7550), __webpack_require__.e(1397), __webpack_require__.e(6051), __webpack_require__.e(5306), __webpack_require__.e(6787), __webpack_require__.e(8994), __webpack_require__.e(9617), __webpack_require__.e(2764), __webpack_require__.e(2558), __webpack_require__.e(1420), __webpack_require__.e(96), __webpack_require__.e(8671), __webpack_require__.e(9956), __webpack_require__.e(2891), __webpack_require__.e(348), __webpack_require__.e(4197), __webpack_require__.e(2538), __webpack_require__.e(9783)]).then(__webpack_require__.bind(__webpack_require__, 9783));
});

// Simple 404 component
var NotFound = function NotFound() {
  return /*#__PURE__*/react.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '50px',
      fontFamily: 'Arial, sans-serif'
    }
  }, /*#__PURE__*/react.createElement("h1", null, "404 - Page Not Found"), /*#__PURE__*/react.createElement("p", null, "The page you're looking for doesn't exist."), /*#__PURE__*/react.createElement("a", {
    href: "/",
    style: {
      color: '#1890ff'
    }
  }, "Go Home"));
};

// Simple About component
var About = function About() {
  return /*#__PURE__*/react.createElement("div", {
    style: {
      padding: '24px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'Arial, sans-serif'
    }
  }, /*#__PURE__*/react.createElement("h1", null, "About App Builder 201"), /*#__PURE__*/react.createElement("p", null, "App Builder 201 is a modern web application builder designed to help you create applications quickly and efficiently."), /*#__PURE__*/react.createElement("h2", null, "Features"), /*#__PURE__*/react.createElement("ul", null, /*#__PURE__*/react.createElement("li", null, "Component-based architecture"), /*#__PURE__*/react.createElement("li", null, "Real-time collaboration"), /*#__PURE__*/react.createElement("li", null, "WebSocket integration"), /*#__PURE__*/react.createElement("li", null, "Modern UI components")));
};

/**
 * Minimal Routes Component - Optimized for Bundle Size
 * Only includes essential routes without heavy dependencies
 */
var MinimalRoutes = function MinimalRoutes() {
  return /*#__PURE__*/react.createElement(react.Suspense, {
    fallback: /*#__PURE__*/react.createElement(SimpleLoading, null)
  }, /*#__PURE__*/react.createElement(dist/* Routes */.BV, null, /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/",
    element: /*#__PURE__*/react.createElement(HomePage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/websocket",
    element: /*#__PURE__*/react.createElement(SimpleWebSocketPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/app-builder",
    element: /*#__PURE__*/react.createElement(LightweightAppBuilder, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/about",
    element: /*#__PURE__*/react.createElement(About, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/app-builder",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/app-builder-enhanced",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/app-builder-integrated",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/dashboard",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/projects",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/templates",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/profile",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/settings",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/login",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/register",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/forgot-password",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/test/*",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/debug/*",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/",
      replace: true
    })
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "*",
    element: /*#__PURE__*/react.createElement(NotFound, null)
  })));
};
/* harmony default export */ const src_MinimalRoutes = (MinimalRoutes);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(3029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(2901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js
var possibleConstructorReturn = __webpack_require__(6822);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js
var getPrototypeOf = __webpack_require__(3954);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/inherits.js
var inherits = __webpack_require__(5501);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4467);
// EXTERNAL MODULE: ./node_modules/antd/es/result/index.js
var result = __webpack_require__(8221);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(1850);
;// ./src/components/SimpleErrorBoundary.js






function _callSuper(t, o, e) { return o = (0,getPrototypeOf/* default */.A)(o), (0,possibleConstructorReturn/* default */.A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,getPrototypeOf/* default */.A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }




/**
 * Simple Error Boundary - Optimized for Bundle Size
 * Basic error handling without styled-components dependencies
 */
var SimpleErrorBoundary = /*#__PURE__*/function (_React$Component) {
  function SimpleErrorBoundary(props) {
    var _this;
    (0,classCallCheck/* default */.A)(this, SimpleErrorBoundary);
    _this = _callSuper(this, SimpleErrorBoundary, [props]);
    (0,defineProperty/* default */.A)(_this, "handleReload", function () {
      window.location.reload();
    });
    (0,defineProperty/* default */.A)(_this, "handleGoHome", function () {
      window.location.href = '/';
    });
    _this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
    return _this;
  }
  (0,inherits/* default */.A)(SimpleErrorBoundary, _React$Component);
  return (0,createClass/* default */.A)(SimpleErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      // Log error details
      console.error('Error caught by boundary:', error, errorInfo);
      this.setState({
        error: error,
        errorInfo: errorInfo
      });
    }
  }, {
    key: "render",
    value: function render() {
      if (this.state.hasError) {
        return /*#__PURE__*/react.createElement("div", {
          style: {
            padding: '50px',
            textAlign: 'center',
            minHeight: '400px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }
        }, /*#__PURE__*/react.createElement(result/* default */.Ay, {
          status: "error",
          title: "Something went wrong",
          subTitle: "An unexpected error occurred. Please try reloading the page.",
          extra: [/*#__PURE__*/react.createElement(es_button/* default */.Ay, {
            type: "primary",
            key: "reload",
            onClick: this.handleReload
          }, "Reload Page"), /*#__PURE__*/react.createElement(es_button/* default */.Ay, {
            key: "home",
            onClick: this.handleGoHome
          }, "Go Home")]
        },  false && /*#__PURE__*/0));
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      // Update state so the next render will show the fallback UI
      return {
        hasError: true
      };
    }
  }]);
}(react.Component);
/* harmony default export */ const components_SimpleErrorBoundary = (SimpleErrorBoundary);
;// ./src/MinimalApp.js





/**
 * Ultra-Minimal App Component - Optimized for Bundle Size
 * Removes all heavy providers and components to achieve sub-1MB bundle
 */
var MinimalApp = function MinimalApp() {
  return /*#__PURE__*/react.createElement(dist/* BrowserRouter */.Kd, {
    future: {
      v7_startTransition: true,
      v7_relativeSplatPath: true
    }
  }, /*#__PURE__*/react.createElement(components_SimpleErrorBoundary, null, /*#__PURE__*/react.createElement(src_MinimalRoutes, null)));
};
/* harmony default export */ const src_MinimalApp = (MinimalApp);
;// ./src/minimal-styles.css
// extracted by mini-css-extract-plugin

;// ./src/minimal-index.js




var root = (0,client/* createRoot */.H)(document.getElementById('root'));
root.render(/*#__PURE__*/react.createElement(react.StrictMode, null, /*#__PURE__*/react.createElement(src_MinimalApp, null)));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, [4644,5108,978,5611,7020,5547,1724,8014,1843,1239,921,8492,6164,3575,3370,3112,6741,1021,7557,8732,4976,7767,5588,5874], () => (__webpack_exec__(6251)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);