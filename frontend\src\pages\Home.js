import React from 'react';
import Card from 'antd/es/card';
import Typography from 'antd/es/typography';
import Button from 'antd/es/button';
import Space from 'antd/es/space';
import Row from 'antd/es/row';
import Col from 'antd/es/col';
import AppstoreOutlined from '@ant-design/icons/es/icons/AppstoreOutlined';
import WifiOutlined from '@ant-design/icons/es/icons/WifiOutlined';
import InfoCircleOutlined from '@ant-design/icons/es/icons/InfoCircleOutlined';

const { Title, Paragraph } = Typography;

/**
 * Simple Home Page - Optimized for Bundle Size
 * Basic landing page without heavy dependencies
 */
const Home = () => {
  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Hero Section */}
      <div style={{ textAlign: 'center', marginBottom: '48px' }}>
        <Title level={1} style={{ fontSize: '3rem', marginBottom: '16px' }}>
          <AppstoreOutlined style={{ marginRight: '16px', color: '#1890ff' }} />
          App Builder 201
        </Title>
        <Paragraph style={{ fontSize: '1.2rem', color: '#666', maxWidth: '600px', margin: '0 auto' }}>
          Build modern web applications with ease. Create, collaborate, and deploy
          your ideas with our intuitive app building platform.
        </Paragraph>
      </div>

      {/* Feature Cards */}
      <Row gutter={[24, 24]} style={{ marginBottom: '48px' }}>
        <Col xs={24} md={8}>
          <Card
            hoverable
            style={{ height: '100%', textAlign: 'center' }}
            bodyStyle={{ padding: '32px 24px' }}
          >
            <AppstoreOutlined style={{ fontSize: '3rem', color: '#1890ff', marginBottom: '16px' }} />
            <Title level={3}>App Builder</Title>
            <Paragraph>
              Build applications visually with our lightweight, optimized interface. Create components, design layouts, and export code.
            </Paragraph>
            <Button type="primary" href="/app-builder">
              Start Building
            </Button>
          </Card>
        </Col>

        <Col xs={24} md={8}>
          <Card
            hoverable
            style={{ height: '100%', textAlign: 'center' }}
            bodyStyle={{ padding: '32px 24px' }}
          >
            <WifiOutlined style={{ fontSize: '3rem', color: '#52c41a', marginBottom: '16px' }} />
            <Title level={3}>Real-time Collaboration</Title>
            <Paragraph>
              Work together with your team in real-time using WebSocket technology.
            </Paragraph>
            <Button type="primary" href="/websocket">
              Test WebSocket
            </Button>
          </Card>
        </Col>

        <Col xs={24} md={8}>
          <Card
            hoverable
            style={{ height: '100%', textAlign: 'center' }}
            bodyStyle={{ padding: '32px 24px' }}
          >
            <InfoCircleOutlined style={{ fontSize: '3rem', color: '#fa8c16', marginBottom: '16px' }} />
            <Title level={3}>Learn More</Title>
            <Paragraph>
              Discover all the features and capabilities of App Builder 201.
            </Paragraph>
            <Button href="/about">
              About
            </Button>
          </Card>
        </Col>
      </Row>

      {/* Quick Start */}
      <Card style={{ textAlign: 'center', background: '#f8f9fa' }}>
        <Title level={2}>Ready to Get Started?</Title>
        <Paragraph style={{ fontSize: '1.1rem', marginBottom: '24px' }}>
          App Builder 201 is currently optimized for performance with a minimal bundle size.
          More features will be available as we continue development.
        </Paragraph>
        <Space size="large">
          <Button type="primary" size="large" href="/websocket">
            Try WebSocket Demo
          </Button>
          <Button size="large" href="/about">
            Learn More
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default Home;
