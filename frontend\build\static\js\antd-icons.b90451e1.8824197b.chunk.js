"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5240],{

/***/ 33704:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _ant_design_icons_svg_es_asn_DashboardTwoTone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(81567);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12226);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DashboardTwoTone = function DashboardTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DashboardTwoToneSvg
  }));
};

/**![dashboard](data:image/svg+xml;base64,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) */
var RefIcon = /*#__PURE__*/(/* unused pure expression or super */ null && (React.forwardRef(DashboardTwoTone)));
if (false) {}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (RefIcon)));

/***/ }),

/***/ 46604:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _ant_design_icons_svg_es_asn_DashOutlined__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(18151);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12226);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DashOutlined = function DashOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DashOutlinedSvg
  }));
};

/**![dash](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTExMiA0NzZoMTYwdjcySDExMnptMzIwIDBoMTYwdjcySDQzMnptMzIwIDBoMTYwdjcySDc1MnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/(/* unused pure expression or super */ null && (React.forwardRef(DashOutlined)));
if (false) {}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (RefIcon)));

/***/ }),

/***/ 92012:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _ant_design_icons_svg_es_asn_DashboardFilled__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5937);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12226);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DashboardFilled = function DashboardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DashboardFilledSvg
  }));
};

/**![dashboard](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNC44IDM4NS42YTQ0Ni43IDQ0Ni43IDAgMDAtOTYtMTQyLjQgNDQ2LjcgNDQ2LjcgMCAwMC0xNDIuNC05NkM2MzEuMSAxMjMuOCA1NzIuNSAxMTIgNTEyIDExMnMtMTE5LjEgMTEuOC0xNzQuNCAzNS4yYTQ0Ni43IDQ0Ni43IDAgMDAtMTQyLjQgOTYgNDQ2LjcgNDQ2LjcgMCAwMC05NiAxNDIuNEM3NS44IDQ0MC45IDY0IDQ5OS41IDY0IDU2MGMwIDEzMi43IDU4LjMgMjU3LjcgMTU5LjkgMzQzLjFsMS43IDEuNGM1LjggNC44IDEzLjEgNy41IDIwLjYgNy41aDUzMS43YzcuNSAwIDE0LjgtMi43IDIwLjYtNy41bDEuNy0xLjRDOTAxLjcgODE3LjcgOTYwIDY5Mi43IDk2MCA1NjBjMC02MC41LTExLjktMTE5LjEtMzUuMi0xNzQuNHpNNDgyIDIzMmMwLTQuNCAzLjYtOCA4LThoNDRjNC40IDAgOCAzLjYgOCA4djgwYzAgNC40LTMuNiA4LTggOGgtNDRjLTQuNCAwLTgtMy42LTgtOHYtODB6TTI3MCA1ODJjMCA0LjQtMy42IDgtOCA4aC04MGMtNC40IDAtOC0zLjYtOC04di00NGMwLTQuNCAzLjYtOCA4LThoODBjNC40IDAgOCAzLjYgOCA4djQ0em05MC43LTIwNC41bC0zMS4xIDMxLjFhOC4wMyA4LjAzIDAgMDEtMTEuMyAwTDI2MS43IDM1MmE4LjAzIDguMDMgMCAwMTAtMTEuM2wzMS4xLTMxLjFjMy4xLTMuMSA4LjItMy4xIDExLjMgMGw1Ni42IDU2LjZjMy4xIDMuMSAzLjEgOC4yIDAgMTEuM3ptMjkxLjEgODMuNmwtODQuNSA4NC41YzUgMTguNy4yIDM5LjQtMTQuNSA1NC4xYTU1Ljk1IDU1Ljk1IDAgMDEtNzkuMiAwIDU1Ljk1IDU1Ljk1IDAgMDEwLTc5LjIgNTUuODcgNTUuODcgMCAwMTU0LjEtMTQuNWw4NC41LTg0LjVjMy4xLTMuMSA4LjItMy4xIDExLjMgMGwyOC4zIDI4LjNjMy4xIDMuMSAzLjEgOC4xIDAgMTEuM3ptNDMtNTIuNGwtMzEuMS0zMS4xYTguMDMgOC4wMyAwIDAxMC0xMS4zbDU2LjYtNTYuNmMzLjEtMy4xIDguMi0zLjEgMTEuMyAwbDMxLjEgMzEuMWMzLjEgMy4xIDMuMSA4LjIgMCAxMS4zbC01Ni42IDU2LjZhOC4wMyA4LjAzIDAgMDEtMTEuMyAwek04NDYgNTgyYzAgNC40LTMuNiA4LTggOGgtODBjLTQuNCAwLTgtMy42LTgtOHYtNDRjMC00LjQgMy42LTggOC04aDgwYzQuNCAwIDggMy42IDggOHY0NHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/(/* unused pure expression or super */ null && (React.forwardRef(DashboardFilled)));
if (false) {}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (RefIcon)));

/***/ }),

/***/ 95142:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _ant_design_icons_svg_es_asn_DashboardOutlined__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(55167);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12226);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DashboardOutlined = function DashboardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DashboardOutlinedSvg
  }));
};

/**![dashboard](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNC44IDM4NS42YTQ0Ni43IDQ0Ni43IDAgMDAtOTYtMTQyLjQgNDQ2LjcgNDQ2LjcgMCAwMC0xNDIuNC05NkM2MzEuMSAxMjMuOCA1NzIuNSAxMTIgNTEyIDExMnMtMTE5LjEgMTEuOC0xNzQuNCAzNS4yYTQ0Ni43IDQ0Ni43IDAgMDAtMTQyLjQgOTYgNDQ2LjcgNDQ2LjcgMCAwMC05NiAxNDIuNEM3NS44IDQ0MC45IDY0IDQ5OS41IDY0IDU2MGMwIDEzMi43IDU4LjMgMjU3LjcgMTU5LjkgMzQzLjFsMS43IDEuNGM1LjggNC44IDEzLjEgNy41IDIwLjYgNy41aDUzMS43YzcuNSAwIDE0LjgtMi43IDIwLjYtNy41bDEuNy0xLjRDOTAxLjcgODE3LjcgOTYwIDY5Mi43IDk2MCA1NjBjMC02MC41LTExLjktMTE5LjEtMzUuMi0xNzQuNHpNNzYxLjQgODM2SDI2Mi42QTM3MS4xMiAzNzEuMTIgMCAwMTE0MCA1NjBjMC05OS40IDM4LjctMTkyLjggMTA5LTI2MyA3MC4zLTcwLjMgMTYzLjctMTA5IDI2My0xMDkgOTkuNCAwIDE5Mi44IDM4LjcgMjYzIDEwOSA3MC4zIDcwLjMgMTA5IDE2My43IDEwOSAyNjMgMCAxMDUuNi00NC41IDIwNS41LTEyMi42IDI3NnpNNjIzLjUgNDIxLjVhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDUyNy43IDUwNmMtMTguNy01LTM5LjQtLjItNTQuMSAxNC41YTU1Ljk1IDU1Ljk1IDAgMDAwIDc5LjIgNTUuOTUgNTUuOTUgMCAwMDc5LjIgMCA1NS44NyA1NS44NyAwIDAwMTQuNS01NC4xbDg0LjUtODQuNWMzLjEtMy4xIDMuMS04LjIgMC0xMS4zbC0yOC4zLTI4LjN6TTQ5MCAzMjBoNDRjNC40IDAgOC0zLjYgOC04di04MGMwLTQuNC0zLjYtOC04LThoLTQ0Yy00LjQgMC04IDMuNi04IDh2ODBjMCA0LjQgMy42IDggOCA4em0yNjAgMjE4djQ0YzAgNC40IDMuNiA4IDggOGg4MGM0LjQgMCA4LTMuNiA4LTh2LTQ0YzAtNC40LTMuNi04LTgtOGgtODBjLTQuNCAwLTggMy42LTggOHptMTIuNy0xOTcuMmwtMzEuMS0zMS4xYTguMDMgOC4wMyAwIDAwLTExLjMgMGwtNTYuNiA1Ni42YTguMDMgOC4wMyAwIDAwMCAxMS4zbDMxLjEgMzEuMWMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDU2LjYtNTYuNmMzLjEtMy4xIDMuMS04LjIgMC0xMS4zem0tNDU4LjYtMzEuMWE4LjAzIDguMDMgMCAwMC0xMS4zIDBsLTMxLjEgMzEuMWE4LjAzIDguMDMgMCAwMDAgMTEuM2w1Ni42IDU2LjZjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGwzMS4xLTMxLjFjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM2wtNTYuNi01Ni42ek0yNjIgNTMwaC04MGMtNC40IDAtOCAzLjYtOCA4djQ0YzAgNC40IDMuNiA4IDggOGg4MGM0LjQgMCA4LTMuNiA4LTh2LTQ0YzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/(/* unused pure expression or super */ null && (React.forwardRef(DashboardOutlined)));
if (false) {}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (RefIcon)));

/***/ })

}]);