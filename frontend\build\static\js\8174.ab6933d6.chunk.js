"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8174],{

/***/ 8174:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6540);
/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(677);
/* harmony import */ var antd_es_typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5475);
/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1850);
/* harmony import */ var antd_es_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8392);
/* harmony import */ var antd_es_row__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7152);
/* harmony import */ var antd_es_col__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6370);
/* harmony import */ var _ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8244);
/* harmony import */ var _ant_design_icons_es_icons_WifiOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(8165);
/* harmony import */ var _ant_design_icons_es_icons_InfoCircleOutlined__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(464);










var Title = antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.Title,
  Paragraph = antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.Paragraph;

/**
 * Simple Home Page - Optimized for Bundle Size
 * Basic landing page without heavy dependencies
 */
var Home = function Home() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      textAlign: 'center',
      marginBottom: '48px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 1,
    style: {
      fontSize: '3rem',
      marginBottom: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
    style: {
      marginRight: '16px',
      color: '#1890ff'
    }
  }), "App Builder 201"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, {
    style: {
      fontSize: '1.2rem',
      color: '#666',
      maxWidth: '600px',
      margin: '0 auto'
    }
  }, "Build modern web applications with ease. Create, collaborate, and deploy your ideas with our intuitive app building platform.")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_row__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, {
    gutter: [24, 24],
    style: {
      marginBottom: '48px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A, {
    hoverable: true,
    style: {
      height: '100%',
      textAlign: 'center'
    },
    bodyStyle: {
      padding: '32px 24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons_es_icons_AppstoreOutlined__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
    style: {
      fontSize: '3rem',
      color: '#1890ff',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 3
  }, "App Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Build applications visually with our lightweight, optimized interface. Create components, design layouts, and export code."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, {
    type: "primary",
    href: "/app-builder"
  }, "Start Building"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A, {
    hoverable: true,
    style: {
      height: '100%',
      textAlign: 'center'
    },
    bodyStyle: {
      padding: '32px 24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons_es_icons_WifiOutlined__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
    style: {
      fontSize: '3rem',
      color: '#52c41a',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 3
  }, "Real-time Collaboration"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Work together with your team in real-time using WebSocket technology."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, {
    type: "primary",
    href: "/websocket"
  }, "Test WebSocket"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_col__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A, {
    hoverable: true,
    style: {
      height: '100%',
      textAlign: 'center'
    },
    bodyStyle: {
      padding: '32px 24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ant_design_icons_es_icons_InfoCircleOutlined__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
    style: {
      fontSize: '3rem',
      color: '#fa8c16',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 3
  }, "Learn More"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, null, "Discover all the features and capabilities of App Builder 201."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, {
    href: "/about"
  }, "About")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_card__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A, {
    style: {
      textAlign: 'center',
      background: '#f8f9fa'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, {
    level: 2
  }, "Ready to Get Started?"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Paragraph, {
    style: {
      fontSize: '1.1rem',
      marginBottom: '24px'
    }
  }, "App Builder 201 is currently optimized for performance with a minimal bundle size. More features will be available as we continue development."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_space__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    size: "large"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, {
    type: "primary",
    size: "large",
    href: "/websocket"
  }, "Try WebSocket Demo"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, {
    size: "large",
    href: "/about"
  }, "Learn More"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);

/***/ })

}]);